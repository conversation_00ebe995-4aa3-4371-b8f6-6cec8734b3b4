#!/usr/bin/env python3
"""
Телеграм ботни тест қилиш учун скрипт
"""

import asyncio
import sys
import os

# Лойиха папкасини Python path га қўшиш
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from telegram_bot import ReliefCalculator
    print("✅ telegram_bot модули муваффақиятли импорт қилинди")
except ImportError as e:
    print(f"❌ telegram_bot модулини импорт қилишда хато: {e}")
    print("Кутубхоналарни ўрнатинг: pip install python-telegram-bot aiohttp pandas openpyxl")
    sys.exit(1)

async def test_calculator():
    """Калькуляторни тест қилиш"""
    print("\n🧪 Калькуляторни тест қилиш...")
    
    calculator = ReliefCalculator()
    
    # Тест маълумотлари
    test_data = """41.2988891601562 69.1905517578125;180;890
41.2849998474121 69.1852798461914;20;670"""
    
    print(f"📝 Тест маълумотлари:\n{test_data}")
    
    # Маълумотларни парсинг қилиш
    data = calculator.parse_coordinate_data(test_data)
    print(f"✅ {len(data)} та қатор парсинг қилинди")
    
    for item in data:
        print(f"   - Қатор {item['row']}: {item['lat1']}, {item['lon1']}, {item['azimuth']}°, {item['distance']}м")
    
    # Координата хисоблашни тест қилиш
    if data:
        item = data[0]
        coord2 = calculator.calculate_second_coordinate(
            item['lat1'], item['lon1'], item['azimuth'], item['distance']
        )
        print(f"🧮 Иккинчи координата: {coord2['lat']:.6f}, {coord2['lon']:.6f}")
    
    # API тестини ўтказиш (агар интернет бор бўлса)
    try:
        print("\n🌐 Mapbox API тести...")
        elevation = await calculator.get_elevation(41.2995, 69.2401)  # Тошкент
        print(f"📏 Тошкент баландлиги: {elevation}м")
        
        if elevation > 0:
            print("✅ API ишламоқда!")
        else:
            print("⚠️ API дан маълумот олинмади (лимит ёки хато)")
            
    except Exception as e:
        print(f"❌ API тестида хато: {e}")
    
    finally:
        await calculator.close_session()
    
    print("\n✅ Тест тугади!")

def test_imports():
    """Кутубхоналарни текшириш"""
    print("📦 Кутубхоналарни текшириш...")
    
    required_modules = [
        'telegram',
        'aiohttp', 
        'pandas',
        'openpyxl'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - ўрнатилмаган")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n❌ Қуйидаги кутубхоналарни ўрнатинг:")
        print("pip install python-telegram-bot aiohttp pandas openpyxl")
        return False
    
    print("✅ Барча кутубхоналар мавжуд!")
    return True

def main():
    """Асосий функция"""
    print("🤖 Телеграм бот тести")
    print("=" * 50)
    
    # Кутубхоналарни текшириш
    if not test_imports():
        return
    
    # Калькуляторни тест қилиш
    try:
        asyncio.run(test_calculator())
    except KeyboardInterrupt:
        print("\n⏹️ Тест тўхтатилди")
    except Exception as e:
        print(f"\n❌ Тестда хато: {e}")

if __name__ == "__main__":
    main()
