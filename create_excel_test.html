<!DOCTYPE html>
<html>
<head>
    <title>Excel файл яратиш</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <h1>Тест учун Excel файл яратиш</h1>
    <button onclick="createExcel()">Excel файл яратиш</button>
    
    <script>
        function createExcel() {
            // Тест маълумотлари
            const data = [
                ['41.2988891601562 69.1905517578125;180;890'],
                ['41.2849998474121 69.1852798461914;20;670'],
                ['41.3000000000000 69.2000000000000;90;1000'],
                ['41.2500000000000 69.1500000000000;45;750'],
                ['41.3500000000000 69.2500000000000;270;1200']
            ];
            
            // Workbook яратиш
            const workbook = XLSX.utils.book_new();
            const worksheet = XLSX.utils.aoa_to_sheet(data);
            
            // Worksheet қўшиш
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Координаталар');
            
            // Файлни юклаб олиш
            XLSX.writeFile(workbook, 'test_coordinates.xlsx');
            
            alert('test_coordinates.xlsx файли яратилди!');
        }
    </script>
</body>
</html>
