# This file is automatically generated by tools/idna-data

__version__ = '15.1.0'
scripts = {
    'Greek': (
        0x37000000374,
        0x37500000378,
        0x37a0000037e,
        0x37f00000380,
        0x38400000385,
        0x38600000387,
        0x3880000038b,
        0x38c0000038d,
        0x38e000003a2,
        0x3a3000003e2,
        0x3f000000400,
        0x1d2600001d2b,
        0x1d5d00001d62,
        0x1d6600001d6b,
        0x1dbf00001dc0,
        0x1f0000001f16,
        0x1f1800001f1e,
        0x1f2000001f46,
        0x1f4800001f4e,
        0x1f5000001f58,
        0x1f5900001f5a,
        0x1f5b00001f5c,
        0x1f5d00001f5e,
        0x1f5f00001f7e,
        0x1f8000001fb5,
        0x1fb600001fc5,
        0x1fc600001fd4,
        0x1fd600001fdc,
        0x1fdd00001ff0,
        0x1ff200001ff5,
        0x1ff600001fff,
        0x212600002127,
        0xab650000ab66,
        0x101400001018f,
        0x101a0000101a1,
        0x1d2000001d246,
    ),
    'Han': (
        0x2e8000002e9a,
        0x2e9b00002ef4,
        0x2f0000002fd6,
        0x300500003006,
        0x300700003008,
        0x30210000302a,
        0x30380000303c,
        0x340000004dc0,
        0x4e000000a000,
        0xf9000000fa6e,
        0xfa700000fada,
        0x16fe200016fe4,
        0x16ff000016ff2,
        0x200000002a6e0,
        0x2a7000002b73a,
        0x2b7400002b81e,
        0x2b8200002cea2,
        0x2ceb00002ebe1,
        0x2ebf00002ee5e,
        0x2f8000002fa1e,
        0x300000003134b,
        0x31350000323b0,
    ),
    'Hebrew': (
        0x591000005c8,
        0x5d0000005eb,
        0x5ef000005f5,
        0xfb1d0000fb37,
        0xfb380000fb3d,
        0xfb3e0000fb3f,
        0xfb400000fb42,
        0xfb430000fb45,
        0xfb460000fb50,
    ),
    'Hiragana': (
        0x304100003097,
        0x309d000030a0,
        0x1b0010001b120,
        0x1b1320001b133,
        0x1b1500001b153,
        0x1f2000001f201,
    ),
    'Katakana': (
        0x30a1000030fb,
        0x30fd00003100,
        0x31f000003200,
        0x32d0000032ff,
        0x330000003358,
        0xff660000ff70,
        0xff710000ff9e,
        0x1aff00001aff4,
        0x1aff50001affc,
        0x1affd0001afff,
        0x1b0000001b001,
        0x1b1200001b123,
        0x1b1550001b156,
        0x1b1640001b168,
    ),
}
joining_types = {
    0xad: 84,
    0x300: 84,
    0x301: 84,
    0x302: 84,
    0x303: 84,
    0x304: 84,
    0x305: 84,
    0x306: 84,
    0x307: 84,
    0x308: 84,
    0x309: 84,
    0x30a: 84,
    0x30b: 84,
    0x30c: 84,
    0x30d: 84,
    0x30e: 84,
    0x30f: 84,
    0x310: 84,
    0x311: 84,
    0x312: 84,
    0x313: 84,
    0x314: 84,
    0x315: 84,
    0x316: 84,
    0x317: 84,
    0x318: 84,
    0x319: 84,
    0x31a: 84,
    0x31b: 84,
    0x31c: 84,
    0x31d: 84,
    0x31e: 84,
    0x31f: 84,
    0x320: 84,
    0x321: 84,
    0x322: 84,
    0x323: 84,
    0x324: 84,
    0x325: 84,
    0x326: 84,
    0x327: 84,
    0x328: 84,
    0x329: 84,
    0x32a: 84,
    0x32b: 84,
    0x32c: 84,
    0x32d: 84,
    0x32e: 84,
    0x32f: 84,
    0x330: 84,
    0x331: 84,
    0x332: 84,
    0x333: 84,
    0x334: 84,
    0x335: 84,
    0x336: 84,
    0x337: 84,
    0x338: 84,
    0x339: 84,
    0x33a: 84,
    0x33b: 84,
    0x33c: 84,
    0x33d: 84,
    0x33e: 84,
    0x33f: 84,
    0x340: 84,
    0x341: 84,
    0x342: 84,
    0x343: 84,
    0x344: 84,
    0x345: 84,
    0x346: 84,
    0x347: 84,
    0x348: 84,
    0x349: 84,
    0x34a: 84,
    0x34b: 84,
    0x34c: 84,
    0x34d: 84,
    0x34e: 84,
    0x34f: 84,
    0x350: 84,
    0x351: 84,
    0x352: 84,
    0x353: 84,
    0x354: 84,
    0x355: 84,
    0x356: 84,
    0x357: 84,
    0x358: 84,
    0x359: 84,
    0x35a: 84,
    0x35b: 84,
    0x35c: 84,
    0x35d: 84,
    0x35e: 84,
    0x35f: 84,
    0x360: 84,
    0x361: 84,
    0x362: 84,
    0x363: 84,
    0x364: 84,
    0x365: 84,
    0x366: 84,
    0x367: 84,
    0x368: 84,
    0x369: 84,
    0x36a: 84,
    0x36b: 84,
    0x36c: 84,
    0x36d: 84,
    0x36e: 84,
    0x36f: 84,
    0x483: 84,
    0x484: 84,
    0x485: 84,
    0x486: 84,
    0x487: 84,
    0x488: 84,
    0x489: 84,
    0x591: 84,
    0x592: 84,
    0x593: 84,
    0x594: 84,
    0x595: 84,
    0x596: 84,
    0x597: 84,
    0x598: 84,
    0x599: 84,
    0x59a: 84,
    0x59b: 84,
    0x59c: 84,
    0x59d: 84,
    0x59e: 84,
    0x59f: 84,
    0x5a0: 84,
    0x5a1: 84,
    0x5a2: 84,
    0x5a3: 84,
    0x5a4: 84,
    0x5a5: 84,
    0x5a6: 84,
    0x5a7: 84,
    0x5a8: 84,
    0x5a9: 84,
    0x5aa: 84,
    0x5ab: 84,
    0x5ac: 84,
    0x5ad: 84,
    0x5ae: 84,
    0x5af: 84,
    0x5b0: 84,
    0x5b1: 84,
    0x5b2: 84,
    0x5b3: 84,
    0x5b4: 84,
    0x5b5: 84,
    0x5b6: 84,
    0x5b7: 84,
    0x5b8: 84,
    0x5b9: 84,
    0x5ba: 84,
    0x5bb: 84,
    0x5bc: 84,
    0x5bd: 84,
    0x5bf: 84,
    0x5c1: 84,
    0x5c2: 84,
    0x5c4: 84,
    0x5c5: 84,
    0x5c7: 84,
    0x610: 84,
    0x611: 84,
    0x612: 84,
    0x613: 84,
    0x614: 84,
    0x615: 84,
    0x616: 84,
    0x617: 84,
    0x618: 84,
    0x619: 84,
    0x61a: 84,
    0x61c: 84,
    0x620: 68,
    0x622: 82,
    0x623: 82,
    0x624: 82,
    0x625: 82,
    0x626: 68,
    0x627: 82,
    0x628: 68,
    0x629: 82,
    0x62a: 68,
    0x62b: 68,
    0x62c: 68,
    0x62d: 68,
    0x62e: 68,
    0x62f: 82,
    0x630: 82,
    0x631: 82,
    0x632: 82,
    0x633: 68,
    0x634: 68,
    0x635: 68,
    0x636: 68,
    0x637: 68,
    0x638: 68,
    0x639: 68,
    0x63a: 68,
    0x63b: 68,
    0x63c: 68,
    0x63d: 68,
    0x63e: 68,
    0x63f: 68,
    0x640: 67,
    0x641: 68,
    0x642: 68,
    0x643: 68,
    0x644: 68,
    0x645: 68,
    0x646: 68,
    0x647: 68,
    0x648: 82,
    0x649: 68,
    0x64a: 68,
    0x64b: 84,
    0x64c: 84,
    0x64d: 84,
    0x64e: 84,
    0x64f: 84,
    0x650: 84,
    0x651: 84,
    0x652: 84,
    0x653: 84,
    0x654: 84,
    0x655: 84,
    0x656: 84,
    0x657: 84,
    0x658: 84,
    0x659: 84,
    0x65a: 84,
    0x65b: 84,
    0x65c: 84,
    0x65d: 84,
    0x65e: 84,
    0x65f: 84,
    0x66e: 68,
    0x66f: 68,
    0x670: 84,
    0x671: 82,
    0x672: 82,
    0x673: 82,
    0x675: 82,
    0x676: 82,
    0x677: 82,
    0x678: 68,
    0x679: 68,
    0x67a: 68,
    0x67b: 68,
    0x67c: 68,
    0x67d: 68,
    0x67e: 68,
    0x67f: 68,
    0x680: 68,
    0x681: 68,
    0x682: 68,
    0x683: 68,
    0x684: 68,
    0x685: 68,
    0x686: 68,
    0x687: 68,
    0x688: 82,
    0x689: 82,
    0x68a: 82,
    0x68b: 82,
    0x68c: 82,
    0x68d: 82,
    0x68e: 82,
    0x68f: 82,
    0x690: 82,
    0x691: 82,
    0x692: 82,
    0x693: 82,
    0x694: 82,
    0x695: 82,
    0x696: 82,
    0x697: 82,
    0x698: 82,
    0x699: 82,
    0x69a: 68,
    0x69b: 68,
    0x69c: 68,
    0x69d: 68,
    0x69e: 68,
    0x69f: 68,
    0x6a0: 68,
    0x6a1: 68,
    0x6a2: 68,
    0x6a3: 68,
    0x6a4: 68,
    0x6a5: 68,
    0x6a6: 68,
    0x6a7: 68,
    0x6a8: 68,
    0x6a9: 68,
    0x6aa: 68,
    0x6ab: 68,
    0x6ac: 68,
    0x6ad: 68,
    0x6ae: 68,
    0x6af: 68,
    0x6b0: 68,
    0x6b1: 68,
    0x6b2: 68,
    0x6b3: 68,
    0x6b4: 68,
    0x6b5: 68,
    0x6b6: 68,
    0x6b7: 68,
    0x6b8: 68,
    0x6b9: 68,
    0x6ba: 68,
    0x6bb: 68,
    0x6bc: 68,
    0x6bd: 68,
    0x6be: 68,
    0x6bf: 68,
    0x6c0: 82,
    0x6c1: 68,
    0x6c2: 68,
    0x6c3: 82,
    0x6c4: 82,
    0x6c5: 82,
    0x6c6: 82,
    0x6c7: 82,
    0x6c8: 82,
    0x6c9: 82,
    0x6ca: 82,
    0x6cb: 82,
    0x6cc: 68,
    0x6cd: 82,
    0x6ce: 68,
    0x6cf: 82,
    0x6d0: 68,
    0x6d1: 68,
    0x6d2: 82,
    0x6d3: 82,
    0x6d5: 82,
    0x6d6: 84,
    0x6d7: 84,
    0x6d8: 84,
    0x6d9: 84,
    0x6da: 84,
    0x6db: 84,
    0x6dc: 84,
    0x6df: 84,
    0x6e0: 84,
    0x6e1: 84,
    0x6e2: 84,
    0x6e3: 84,
    0x6e4: 84,
    0x6e7: 84,
    0x6e8: 84,
    0x6ea: 84,
    0x6eb: 84,
    0x6ec: 84,
    0x6ed: 84,
    0x6ee: 82,
    0x6ef: 82,
    0x6fa: 68,
    0x6fb: 68,
    0x6fc: 68,
    0x6ff: 68,
    0x70f: 84,
    0x710: 82,
    0x711: 84,
    0x712: 68,
    0x713: 68,
    0x714: 68,
    0x715: 82,
    0x716: 82,
    0x717: 82,
    0x718: 82,
    0x719: 82,
    0x71a: 68,
    0x71b: 68,
    0x71c: 68,
    0x71d: 68,
    0x71e: 82,
    0x71f: 68,
    0x720: 68,
    0x721: 68,
    0x722: 68,
    0x723: 68,
    0x724: 68,
    0x725: 68,
    0x726: 68,
    0x727: 68,
    0x728: 82,
    0x729: 68,
    0x72a: 82,
    0x72b: 68,
    0x72c: 82,
    0x72d: 68,
    0x72e: 68,
    0x72f: 82,
    0x730: 84,
    0x731: 84,
    0x732: 84,
    0x733: 84,
    0x734: 84,
    0x735: 84,
    0x736: 84,
    0x737: 84,
    0x738: 84,
    0x739: 84,
    0x73a: 84,
    0x73b: 84,
    0x73c: 84,
    0x73d: 84,
    0x73e: 84,
    0x73f: 84,
    0x740: 84,
    0x741: 84,
    0x742: 84,
    0x743: 84,
    0x744: 84,
    0x745: 84,
    0x746: 84,
    0x747: 84,
    0x748: 84,
    0x749: 84,
    0x74a: 84,
    0x74d: 82,
    0x74e: 68,
    0x74f: 68,
    0x750: 68,
    0x751: 68,
    0x752: 68,
    0x753: 68,
    0x754: 68,
    0x755: 68,
    0x756: 68,
    0x757: 68,
    0x758: 68,
    0x759: 82,
    0x75a: 82,
    0x75b: 82,
    0x75c: 68,
    0x75d: 68,
    0x75e: 68,
    0x75f: 68,
    0x760: 68,
    0x761: 68,
    0x762: 68,
    0x763: 68,
    0x764: 68,
    0x765: 68,
    0x766: 68,
    0x767: 68,
    0x768: 68,
    0x769: 68,
    0x76a: 68,
    0x76b: 82,
    0x76c: 82,
    0x76d: 68,
    0x76e: 68,
    0x76f: 68,
    0x770: 68,
    0x771: 82,
    0x772: 68,
    0x773: 82,
    0x774: 82,
    0x775: 68,
    0x776: 68,
    0x777: 68,
    0x778: 82,
    0x779: 82,
    0x77a: 68,
    0x77b: 68,
    0x77c: 68,
    0x77d: 68,
    0x77e: 68,
    0x77f: 68,
    0x7a6: 84,
    0x7a7: 84,
    0x7a8: 84,
    0x7a9: 84,
    0x7aa: 84,
    0x7ab: 84,
    0x7ac: 84,
    0x7ad: 84,
    0x7ae: 84,
    0x7af: 84,
    0x7b0: 84,
    0x7ca: 68,
    0x7cb: 68,
    0x7cc: 68,
    0x7cd: 68,
    0x7ce: 68,
    0x7cf: 68,
    0x7d0: 68,
    0x7d1: 68,
    0x7d2: 68,
    0x7d3: 68,
    0x7d4: 68,
    0x7d5: 68,
    0x7d6: 68,
    0x7d7: 68,
    0x7d8: 68,
    0x7d9: 68,
    0x7da: 68,
    0x7db: 68,
    0x7dc: 68,
    0x7dd: 68,
    0x7de: 68,
    0x7df: 68,
    0x7e0: 68,
    0x7e1: 68,
    0x7e2: 68,
    0x7e3: 68,
    0x7e4: 68,
    0x7e5: 68,
    0x7e6: 68,
    0x7e7: 68,
    0x7e8: 68,
    0x7e9: 68,
    0x7ea: 68,
    0x7eb: 84,
    0x7ec: 84,
    0x7ed: 84,
    0x7ee: 84,
    0x7ef: 84,
    0x7f0: 84,
    0x7f1: 84,
    0x7f2: 84,
    0x7f3: 84,
    0x7fa: 67,
    0x7fd: 84,
    0x816: 84,
    0x817: 84,
    0x818: 84,
    0x819: 84,
    0x81b: 84,
    0x81c: 84,
    0x81d: 84,
    0x81e: 84,
    0x81f: 84,
    0x820: 84,
    0x821: 84,
    0x822: 84,
    0x823: 84,
    0x825: 84,
    0x826: 84,
    0x827: 84,
    0x829: 84,
    0x82a: 84,
    0x82b: 84,
    0x82c: 84,
    0x82d: 84,
    0x840: 82,
    0x841: 68,
    0x842: 68,
    0x843: 68,
    0x844: 68,
    0x845: 68,
    0x846: 82,
    0x847: 82,
    0x848: 68,
    0x849: 82,
    0x84a: 68,
    0x84b: 68,
    0x84c: 68,
    0x84d: 68,
    0x84e: 68,
    0x84f: 68,
    0x850: 68,
    0x851: 68,
    0x852: 68,
    0x853: 68,
    0x854: 82,
    0x855: 68,
    0x856: 82,
    0x857: 82,
    0x858: 82,
    0x859: 84,
    0x85a: 84,
    0x85b: 84,
    0x860: 68,
    0x862: 68,
    0x863: 68,
    0x864: 68,
    0x865: 68,
    0x867: 82,
    0x868: 68,
    0x869: 82,
    0x86a: 82,
    0x870: 82,
    0x871: 82,
    0x872: 82,
    0x873: 82,
    0x874: 82,
    0x875: 82,
    0x876: 82,
    0x877: 82,
    0x878: 82,
    0x879: 82,
    0x87a: 82,
    0x87b: 82,
    0x87c: 82,
    0x87d: 82,
    0x87e: 82,
    0x87f: 82,
    0x880: 82,
    0x881: 82,
    0x882: 82,
    0x883: 67,
    0x884: 67,
    0x885: 67,
    0x886: 68,
    0x889: 68,
    0x88a: 68,
    0x88b: 68,
    0x88c: 68,
    0x88d: 68,
    0x88e: 82,
    0x898: 84,
    0x899: 84,
    0x89a: 84,
    0x89b: 84,
    0x89c: 84,
    0x89d: 84,
    0x89e: 84,
    0x89f: 84,
    0x8a0: 68,
    0x8a1: 68,
    0x8a2: 68,
    0x8a3: 68,
    0x8a4: 68,
    0x8a5: 68,
    0x8a6: 68,
    0x8a7: 68,
    0x8a8: 68,
    0x8a9: 68,
    0x8aa: 82,
    0x8ab: 82,
    0x8ac: 82,
    0x8ae: 82,
    0x8af: 68,
    0x8b0: 68,
    0x8b1: 82,
    0x8b2: 82,
    0x8b3: 68,
    0x8b4: 68,
    0x8b5: 68,
    0x8b6: 68,
    0x8b7: 68,
    0x8b8: 68,
    0x8b9: 82,
    0x8ba: 68,
    0x8bb: 68,
    0x8bc: 68,
    0x8bd: 68,
    0x8be: 68,
    0x8bf: 68,
    0x8c0: 68,
    0x8c1: 68,
    0x8c2: 68,
    0x8c3: 68,
    0x8c4: 68,
    0x8c5: 68,
    0x8c6: 68,
    0x8c7: 68,
    0x8c8: 68,
    0x8ca: 84,
    0x8cb: 84,
    0x8cc: 84,
    0x8cd: 84,
    0x8ce: 84,
    0x8cf: 84,
    0x8d0: 84,
    0x8d1: 84,
    0x8d2: 84,
    0x8d3: 84,
    0x8d4: 84,
    0x8d5: 84,
    0x8d6: 84,
    0x8d7: 84,
    0x8d8: 84,
    0x8d9: 84,
    0x8da: 84,
    0x8db: 84,
    0x8dc: 84,
    0x8dd: 84,
    0x8de: 84,
    0x8df: 84,
    0x8e0: 84,
    0x8e1: 84,
    0x8e3: 84,
    0x8e4: 84,
    0x8e5: 84,
    0x8e6: 84,
    0x8e7: 84,
    0x8e8: 84,
    0x8e9: 84,
    0x8ea: 84,
    0x8eb: 84,
    0x8ec: 84,
    0x8ed: 84,
    0x8ee: 84,
    0x8ef: 84,
    0x8f0: 84,
    0x8f1: 84,
    0x8f2: 84,
    0x8f3: 84,
    0x8f4: 84,
    0x8f5: 84,
    0x8f6: 84,
    0x8f7: 84,
    0x8f8: 84,
    0x8f9: 84,
    0x8fa: 84,
    0x8fb: 84,
    0x8fc: 84,
    0x8fd: 84,
    0x8fe: 84,
    0x8ff: 84,
    0x900: 84,
    0x901: 84,
    0x902: 84,
    0x93a: 84,
    0x93c: 84,
    0x941: 84,
    0x942: 84,
    0x943: 84,
    0x944: 84,
    0x945: 84,
    0x946: 84,
    0x947: 84,
    0x948: 84,
    0x94d: 84,
    0x951: 84,
    0x952: 84,
    0x953: 84,
    0x954: 84,
    0x955: 84,
    0x956: 84,
    0x957: 84,
    0x962: 84,
    0x963: 84,
    0x981: 84,
    0x9bc: 84,
    0x9c1: 84,
    0x9c2: 84,
    0x9c3: 84,
    0x9c4: 84,
    0x9cd: 84,
    0x9e2: 84,
    0x9e3: 84,
    0x9fe: 84,
    0xa01: 84,
    0xa02: 84,
    0xa3c: 84,
    0xa41: 84,
    0xa42: 84,
    0xa47: 84,
    0xa48: 84,
    0xa4b: 84,
    0xa4c: 84,
    0xa4d: 84,
    0xa51: 84,
    0xa70: 84,
    0xa71: 84,
    0xa75: 84,
    0xa81: 84,
    0xa82: 84,
    0xabc: 84,
    0xac1: 84,
    0xac2: 84,
    0xac3: 84,
    0xac4: 84,
    0xac5: 84,
    0xac7: 84,
    0xac8: 84,
    0xacd: 84,
    0xae2: 84,
    0xae3: 84,
    0xafa: 84,
    0xafb: 84,
    0xafc: 84,
    0xafd: 84,
    0xafe: 84,
    0xaff: 84,
    0xb01: 84,
    0xb3c: 84,
    0xb3f: 84,
    0xb41: 84,
    0xb42: 84,
    0xb43: 84,
    0xb44: 84,
    0xb4d: 84,
    0xb55: 84,
    0xb56: 84,
    0xb62: 84,
    0xb63: 84,
    0xb82: 84,
    0xbc0: 84,
    0xbcd: 84,
    0xc00: 84,
    0xc04: 84,
    0xc3c: 84,
    0xc3e: 84,
    0xc3f: 84,
    0xc40: 84,
    0xc46: 84,
    0xc47: 84,
    0xc48: 84,
    0xc4a: 84,
    0xc4b: 84,
    0xc4c: 84,
    0xc4d: 84,
    0xc55: 84,
    0xc56: 84,
    0xc62: 84,
    0xc63: 84,
    0xc81: 84,
    0xcbc: 84,
    0xcbf: 84,
    0xcc6: 84,
    0xccc: 84,
    0xccd: 84,
    0xce2: 84,
    0xce3: 84,
    0xd00: 84,
    0xd01: 84,
    0xd3b: 84,
    0xd3c: 84,
    0xd41: 84,
    0xd42: 84,
    0xd43: 84,
    0xd44: 84,
    0xd4d: 84,
    0xd62: 84,
    0xd63: 84,
    0xd81: 84,
    0xdca: 84,
    0xdd2: 84,
    0xdd3: 84,
    0xdd4: 84,
    0xdd6: 84,
    0xe31: 84,
    0xe34: 84,
    0xe35: 84,
    0xe36: 84,
    0xe37: 84,
    0xe38: 84,
    0xe39: 84,
    0xe3a: 84,
    0xe47: 84,
    0xe48: 84,
    0xe49: 84,
    0xe4a: 84,
    0xe4b: 84,
    0xe4c: 84,
    0xe4d: 84,
    0xe4e: 84,
    0xeb1: 84,
    0xeb4: 84,
    0xeb5: 84,
    0xeb6: 84,
    0xeb7: 84,
    0xeb8: 84,
    0xeb9: 84,
    0xeba: 84,
    0xebb: 84,
    0xebc: 84,
    0xec8: 84,
    0xec9: 84,
    0xeca: 84,
    0xecb: 84,
    0xecc: 84,
    0xecd: 84,
    0xece: 84,
    0xf18: 84,
    0xf19: 84,
    0xf35: 84,
    0xf37: 84,
    0xf39: 84,
    0xf71: 84,
    0xf72: 84,
    0xf73: 84,
    0xf74: 84,
    0xf75: 84,
    0xf76: 84,
    0xf77: 84,
    0xf78: 84,
    0xf79: 84,
    0xf7a: 84,
    0xf7b: 84,
    0xf7c: 84,
    0xf7d: 84,
    0xf7e: 84,
    0xf80: 84,
    0xf81: 84,
    0xf82: 84,
    0xf83: 84,
    0xf84: 84,
    0xf86: 84,
    0xf87: 84,
    0xf8d: 84,
    0xf8e: 84,
    0xf8f: 84,
    0xf90: 84,
    0xf91: 84,
    0xf92: 84,
    0xf93: 84,
    0xf94: 84,
    0xf95: 84,
    0xf96: 84,
    0xf97: 84,
    0xf99: 84,
    0xf9a: 84,
    0xf9b: 84,
    0xf9c: 84,
    0xf9d: 84,
    0xf9e: 84,
    0xf9f: 84,
    0xfa0: 84,
    0xfa1: 84,
    0xfa2: 84,
    0xfa3: 84,
    0xfa4: 84,
    0xfa5: 84,
    0xfa6: 84,
    0xfa7: 84,
    0xfa8: 84,
    0xfa9: 84,
    0xfaa: 84,
    0xfab: 84,
    0xfac: 84,
    0xfad: 84,
    0xfae: 84,
    0xfaf: 84,
    0xfb0: 84,
    0xfb1: 84,
    0xfb2: 84,
    0xfb3: 84,
    0xfb4: 84,
    0xfb5: 84,
    0xfb6: 84,
    0xfb7: 84,
    0xfb8: 84,
    0xfb9: 84,
    0xfba: 84,
    0xfbb: 84,
    0xfbc: 84,
    0xfc6: 84,
    0x102d: 84,
    0x102e: 84,
    0x102f: 84,
    0x1030: 84,
    0x1032: 84,
    0x1033: 84,
    0x1034: 84,
    0x1035: 84,
    0x1036: 84,
    0x1037: 84,
    0x1039: 84,
    0x103a: 84,
    0x103d: 84,
    0x103e: 84,
    0x1058: 84,
    0x1059: 84,
    0x105e: 84,
    0x105f: 84,
    0x1060: 84,
    0x1071: 84,
    0x1072: 84,
    0x1073: 84,
    0x1074: 84,
    0x1082: 84,
    0x1085: 84,
    0x1086: 84,
    0x108d: 84,
    0x109d: 84,
    0x135d: 84,
    0x135e: 84,
    0x135f: 84,
    0x1712: 84,
    0x1713: 84,
    0x1714: 84,
    0x1732: 84,
    0x1733: 84,
    0x1752: 84,
    0x1753: 84,
    0x1772: 84,
    0x1773: 84,
    0x17b4: 84,
    0x17b5: 84,
    0x17b7: 84,
    0x17b8: 84,
    0x17b9: 84,
    0x17ba: 84,
    0x17bb: 84,
    0x17bc: 84,
    0x17bd: 84,
    0x17c6: 84,
    0x17c9: 84,
    0x17ca: 84,
    0x17cb: 84,
    0x17cc: 84,
    0x17cd: 84,
    0x17ce: 84,
    0x17cf: 84,
    0x17d0: 84,
    0x17d1: 84,
    0x17d2: 84,
    0x17d3: 84,
    0x17dd: 84,
    0x1807: 68,
    0x180a: 67,
    0x180b: 84,
    0x180c: 84,
    0x180d: 84,
    0x180f: 84,
    0x1820: 68,
    0x1821: 68,
    0x1822: 68,
    0x1823: 68,
    0x1824: 68,
    0x1825: 68,
    0x1826: 68,
    0x1827: 68,
    0x1828: 68,
    0x1829: 68,
    0x182a: 68,
    0x182b: 68,
    0x182c: 68,
    0x182d: 68,
    0x182e: 68,
    0x182f: 68,
    0x1830: 68,
    0x1831: 68,
    0x1832: 68,
    0x1833: 68,
    0x1834: 68,
    0x1835: 68,
    0x1836: 68,
    0x1837: 68,
    0x1838: 68,
    0x1839: 68,
    0x183a: 68,
    0x183b: 68,
    0x183c: 68,
    0x183d: 68,
    0x183e: 68,
    0x183f: 68,
    0x1840: 68,
    0x1841: 68,
    0x1842: 68,
    0x1843: 68,
    0x1844: 68,
    0x1845: 68,
    0x1846: 68,
    0x1847: 68,
    0x1848: 68,
    0x1849: 68,
    0x184a: 68,
    0x184b: 68,
    0x184c: 68,
    0x184d: 68,
    0x184e: 68,
    0x184f: 68,
    0x1850: 68,
    0x1851: 68,
    0x1852: 68,
    0x1853: 68,
    0x1854: 68,
    0x1855: 68,
    0x1856: 68,
    0x1857: 68,
    0x1858: 68,
    0x1859: 68,
    0x185a: 68,
    0x185b: 68,
    0x185c: 68,
    0x185d: 68,
    0x185e: 68,
    0x185f: 68,
    0x1860: 68,
    0x1861: 68,
    0x1862: 68,
    0x1863: 68,
    0x1864: 68,
    0x1865: 68,
    0x1866: 68,
    0x1867: 68,
    0x1868: 68,
    0x1869: 68,
    0x186a: 68,
    0x186b: 68,
    0x186c: 68,
    0x186d: 68,
    0x186e: 68,
    0x186f: 68,
    0x1870: 68,
    0x1871: 68,
    0x1872: 68,
    0x1873: 68,
    0x1874: 68,
    0x1875: 68,
    0x1876: 68,
    0x1877: 68,
    0x1878: 68,
    0x1885: 84,
    0x1886: 84,
    0x1887: 68,
    0x1888: 68,
    0x1889: 68,
    0x188a: 68,
    0x188b: 68,
    0x188c: 68,
    0x188d: 68,
    0x188e: 68,
    0x188f: 68,
    0x1890: 68,
    0x1891: 68,
    0x1892: 68,
    0x1893: 68,
    0x1894: 68,
    0x1895: 68,
    0x1896: 68,
    0x1897: 68,
    0x1898: 68,
    0x1899: 68,
    0x189a: 68,
    0x189b: 68,
    0x189c: 68,
    0x189d: 68,
    0x189e: 68,
    0x189f: 68,
    0x18a0: 68,
    0x18a1: 68,
    0x18a2: 68,
    0x18a3: 68,
    0x18a4: 68,
    0x18a5: 68,
    0x18a6: 68,
    0x18a7: 68,
    0x18a8: 68,
    0x18a9: 84,
    0x18aa: 68,
    0x1920: 84,
    0x1921: 84,
    0x1922: 84,
    0x1927: 84,
    0x1928: 84,
    0x1932: 84,
    0x1939: 84,
    0x193a: 84,
    0x193b: 84,
    0x1a17: 84,
    0x1a18: 84,
    0x1a1b: 84,
    0x1a56: 84,
    0x1a58: 84,
    0x1a59: 84,
    0x1a5a: 84,
    0x1a5b: 84,
    0x1a5c: 84,
    0x1a5d: 84,
    0x1a5e: 84,
    0x1a60: 84,
    0x1a62: 84,
    0x1a65: 84,
    0x1a66: 84,
    0x1a67: 84,
    0x1a68: 84,
    0x1a69: 84,
    0x1a6a: 84,
    0x1a6b: 84,
    0x1a6c: 84,
    0x1a73: 84,
    0x1a74: 84,
    0x1a75: 84,
    0x1a76: 84,
    0x1a77: 84,
    0x1a78: 84,
    0x1a79: 84,
    0x1a7a: 84,
    0x1a7b: 84,
    0x1a7c: 84,
    0x1a7f: 84,
    0x1ab0: 84,
    0x1ab1: 84,
    0x1ab2: 84,
    0x1ab3: 84,
    0x1ab4: 84,
    0x1ab5: 84,
    0x1ab6: 84,
    0x1ab7: 84,
    0x1ab8: 84,
    0x1ab9: 84,
    0x1aba: 84,
    0x1abb: 84,
    0x1abc: 84,
    0x1abd: 84,
    0x1abe: 84,
    0x1abf: 84,
    0x1ac0: 84,
    0x1ac1: 84,
    0x1ac2: 84,
    0x1ac3: 84,
    0x1ac4: 84,
    0x1ac5: 84,
    0x1ac6: 84,
    0x1ac7: 84,
    0x1ac8: 84,
    0x1ac9: 84,
    0x1aca: 84,
    0x1acb: 84,
    0x1acc: 84,
    0x1acd: 84,
    0x1ace: 84,
    0x1b00: 84,
    0x1b01: 84,
    0x1b02: 84,
    0x1b03: 84,
    0x1b34: 84,
    0x1b36: 84,
    0x1b37: 84,
    0x1b38: 84,
    0x1b39: 84,
    0x1b3a: 84,
    0x1b3c: 84,
    0x1b42: 84,
    0x1b6b: 84,
    0x1b6c: 84,
    0x1b6d: 84,
    0x1b6e: 84,
    0x1b6f: 84,
    0x1b70: 84,
    0x1b71: 84,
    0x1b72: 84,
    0x1b73: 84,
    0x1b80: 84,
    0x1b81: 84,
    0x1ba2: 84,
    0x1ba3: 84,
    0x1ba4: 84,
    0x1ba5: 84,
    0x1ba8: 84,
    0x1ba9: 84,
    0x1bab: 84,
    0x1bac: 84,
    0x1bad: 84,
    0x1be6: 84,
    0x1be8: 84,
    0x1be9: 84,
    0x1bed: 84,
    0x1bef: 84,
    0x1bf0: 84,
    0x1bf1: 84,
    0x1c2c: 84,
    0x1c2d: 84,
    0x1c2e: 84,
    0x1c2f: 84,
    0x1c30: 84,
    0x1c31: 84,
    0x1c32: 84,
    0x1c33: 84,
    0x1c36: 84,
    0x1c37: 84,
    0x1cd0: 84,
    0x1cd1: 84,
    0x1cd2: 84,
    0x1cd4: 84,
    0x1cd5: 84,
    0x1cd6: 84,
    0x1cd7: 84,
    0x1cd8: 84,
    0x1cd9: 84,
    0x1cda: 84,
    0x1cdb: 84,
    0x1cdc: 84,
    0x1cdd: 84,
    0x1cde: 84,
    0x1cdf: 84,
    0x1ce0: 84,
    0x1ce2: 84,
    0x1ce3: 84,
    0x1ce4: 84,
    0x1ce5: 84,
    0x1ce6: 84,
    0x1ce7: 84,
    0x1ce8: 84,
    0x1ced: 84,
    0x1cf4: 84,
    0x1cf8: 84,
    0x1cf9: 84,
    0x1dc0: 84,
    0x1dc1: 84,
    0x1dc2: 84,
    0x1dc3: 84,
    0x1dc4: 84,
    0x1dc5: 84,
    0x1dc6: 84,
    0x1dc7: 84,
    0x1dc8: 84,
    0x1dc9: 84,
    0x1dca: 84,
    0x1dcb: 84,
    0x1dcc: 84,
    0x1dcd: 84,
    0x1dce: 84,
    0x1dcf: 84,
    0x1dd0: 84,
    0x1dd1: 84,
    0x1dd2: 84,
    0x1dd3: 84,
    0x1dd4: 84,
    0x1dd5: 84,
    0x1dd6: 84,
    0x1dd7: 84,
    0x1dd8: 84,
    0x1dd9: 84,
    0x1dda: 84,
    0x1ddb: 84,
    0x1ddc: 84,
    0x1ddd: 84,
    0x1dde: 84,
    0x1ddf: 84,
    0x1de0: 84,
    0x1de1: 84,
    0x1de2: 84,
    0x1de3: 84,
    0x1de4: 84,
    0x1de5: 84,
    0x1de6: 84,
    0x1de7: 84,
    0x1de8: 84,
    0x1de9: 84,
    0x1dea: 84,
    0x1deb: 84,
    0x1dec: 84,
    0x1ded: 84,
    0x1dee: 84,
    0x1def: 84,
    0x1df0: 84,
    0x1df1: 84,
    0x1df2: 84,
    0x1df3: 84,
    0x1df4: 84,
    0x1df5: 84,
    0x1df6: 84,
    0x1df7: 84,
    0x1df8: 84,
    0x1df9: 84,
    0x1dfa: 84,
    0x1dfb: 84,
    0x1dfc: 84,
    0x1dfd: 84,
    0x1dfe: 84,
    0x1dff: 84,
    0x200b: 84,
    0x200d: 67,
    0x200e: 84,
    0x200f: 84,
    0x202a: 84,
    0x202b: 84,
    0x202c: 84,
    0x202d: 84,
    0x202e: 84,
    0x2060: 84,
    0x2061: 84,
    0x2062: 84,
    0x2063: 84,
    0x2064: 84,
    0x206a: 84,
    0x206b: 84,
    0x206c: 84,
    0x206d: 84,
    0x206e: 84,
    0x206f: 84,
    0x20d0: 84,
    0x20d1: 84,
    0x20d2: 84,
    0x20d3: 84,
    0x20d4: 84,
    0x20d5: 84,
    0x20d6: 84,
    0x20d7: 84,
    0x20d8: 84,
    0x20d9: 84,
    0x20da: 84,
    0x20db: 84,
    0x20dc: 84,
    0x20dd: 84,
    0x20de: 84,
    0x20df: 84,
    0x20e0: 84,
    0x20e1: 84,
    0x20e2: 84,
    0x20e3: 84,
    0x20e4: 84,
    0x20e5: 84,
    0x20e6: 84,
    0x20e7: 84,
    0x20e8: 84,
    0x20e9: 84,
    0x20ea: 84,
    0x20eb: 84,
    0x20ec: 84,
    0x20ed: 84,
    0x20ee: 84,
    0x20ef: 84,
    0x20f0: 84,
    0x2cef: 84,
    0x2cf0: 84,
    0x2cf1: 84,
    0x2d7f: 84,
    0x2de0: 84,
    0x2de1: 84,
    0x2de2: 84,
    0x2de3: 84,
    0x2de4: 84,
    0x2de5: 84,
    0x2de6: 84,
    0x2de7: 84,
    0x2de8: 84,
    0x2de9: 84,
    0x2dea: 84,
    0x2deb: 84,
    0x2dec: 84,
    0x2ded: 84,
    0x2dee: 84,
    0x2def: 84,
    0x2df0: 84,
    0x2df1: 84,
    0x2df2: 84,
    0x2df3: 84,
    0x2df4: 84,
    0x2df5: 84,
    0x2df6: 84,
    0x2df7: 84,
    0x2df8: 84,
    0x2df9: 84,
    0x2dfa: 84,
    0x2dfb: 84,
    0x2dfc: 84,
    0x2dfd: 84,
    0x2dfe: 84,
    0x2dff: 84,
    0x302a: 84,
    0x302b: 84,
    0x302c: 84,
    0x302d: 84,
    0x3099: 84,
    0x309a: 84,
    0xa66f: 84,
    0xa670: 84,
    0xa671: 84,
    0xa672: 84,
    0xa674: 84,
    0xa675: 84,
    0xa676: 84,
    0xa677: 84,
    0xa678: 84,
    0xa679: 84,
    0xa67a: 84,
    0xa67b: 84,
    0xa67c: 84,
    0xa67d: 84,
    0xa69e: 84,
    0xa69f: 84,
    0xa6f0: 84,
    0xa6f1: 84,
    0xa802: 84,
    0xa806: 84,
    0xa80b: 84,
    0xa825: 84,
    0xa826: 84,
    0xa82c: 84,
    0xa840: 68,
    0xa841: 68,
    0xa842: 68,
    0xa843: 68,
    0xa844: 68,
    0xa845: 68,
    0xa846: 68,
    0xa847: 68,
    0xa848: 68,
    0xa849: 68,
    0xa84a: 68,
    0xa84b: 68,
    0xa84c: 68,
    0xa84d: 68,
    0xa84e: 68,
    0xa84f: 68,
    0xa850: 68,
    0xa851: 68,
    0xa852: 68,
    0xa853: 68,
    0xa854: 68,
    0xa855: 68,
    0xa856: 68,
    0xa857: 68,
    0xa858: 68,
    0xa859: 68,
    0xa85a: 68,
    0xa85b: 68,
    0xa85c: 68,
    0xa85d: 68,
    0xa85e: 68,
    0xa85f: 68,
    0xa860: 68,
    0xa861: 68,
    0xa862: 68,
    0xa863: 68,
    0xa864: 68,
    0xa865: 68,
    0xa866: 68,
    0xa867: 68,
    0xa868: 68,
    0xa869: 68,
    0xa86a: 68,
    0xa86b: 68,
    0xa86c: 68,
    0xa86d: 68,
    0xa86e: 68,
    0xa86f: 68,
    0xa870: 68,
    0xa871: 68,
    0xa872: 76,
    0xa8c4: 84,
    0xa8c5: 84,
    0xa8e0: 84,
    0xa8e1: 84,
    0xa8e2: 84,
    0xa8e3: 84,
    0xa8e4: 84,
    0xa8e5: 84,
    0xa8e6: 84,
    0xa8e7: 84,
    0xa8e8: 84,
    0xa8e9: 84,
    0xa8ea: 84,
    0xa8eb: 84,
    0xa8ec: 84,
    0xa8ed: 84,
    0xa8ee: 84,
    0xa8ef: 84,
    0xa8f0: 84,
    0xa8f1: 84,
    0xa8ff: 84,
    0xa926: 84,
    0xa927: 84,
    0xa928: 84,
    0xa929: 84,
    0xa92a: 84,
    0xa92b: 84,
    0xa92c: 84,
    0xa92d: 84,
    0xa947: 84,
    0xa948: 84,
    0xa949: 84,
    0xa94a: 84,
    0xa94b: 84,
    0xa94c: 84,
    0xa94d: 84,
    0xa94e: 84,
    0xa94f: 84,
    0xa950: 84,
    0xa951: 84,
    0xa980: 84,
    0xa981: 84,
    0xa982: 84,
    0xa9b3: 84,
    0xa9b6: 84,
    0xa9b7: 84,
    0xa9b8: 84,
    0xa9b9: 84,
    0xa9bc: 84,
    0xa9bd: 84,
    0xa9e5: 84,
    0xaa29: 84,
    0xaa2a: 84,
    0xaa2b: 84,
    0xaa2c: 84,
    0xaa2d: 84,
    0xaa2e: 84,
    0xaa31: 84,
    0xaa32: 84,
    0xaa35: 84,
    0xaa36: 84,
    0xaa43: 84,
    0xaa4c: 84,
    0xaa7c: 84,
    0xaab0: 84,
    0xaab2: 84,
    0xaab3: 84,
    0xaab4: 84,
    0xaab7: 84,
    0xaab8: 84,
    0xaabe: 84,
    0xaabf: 84,
    0xaac1: 84,
    0xaaec: 84,
    0xaaed: 84,
    0xaaf6: 84,
    0xabe5: 84,
    0xabe8: 84,
    0xabed: 84,
    0xfb1e: 84,
    0xfe00: 84,
    0xfe01: 84,
    0xfe02: 84,
    0xfe03: 84,
    0xfe04: 84,
    0xfe05: 84,
    0xfe06: 84,
    0xfe07: 84,
    0xfe08: 84,
    0xfe09: 84,
    0xfe0a: 84,
    0xfe0b: 84,
    0xfe0c: 84,
    0xfe0d: 84,
    0xfe0e: 84,
    0xfe0f: 84,
    0xfe20: 84,
    0xfe21: 84,
    0xfe22: 84,
    0xfe23: 84,
    0xfe24: 84,
    0xfe25: 84,
    0xfe26: 84,
    0xfe27: 84,
    0xfe28: 84,
    0xfe29: 84,
    0xfe2a: 84,
    0xfe2b: 84,
    0xfe2c: 84,
    0xfe2d: 84,
    0xfe2e: 84,
    0xfe2f: 84,
    0xfeff: 84,
    0xfff9: 84,
    0xfffa: 84,
    0xfffb: 84,
    0x101fd: 84,
    0x102e0: 84,
    0x10376: 84,
    0x10377: 84,
    0x10378: 84,
    0x10379: 84,
    0x1037a: 84,
    0x10a01: 84,
    0x10a02: 84,
    0x10a03: 84,
    0x10a05: 84,
    0x10a06: 84,
    0x10a0c: 84,
    0x10a0d: 84,
    0x10a0e: 84,
    0x10a0f: 84,
    0x10a38: 84,
    0x10a39: 84,
    0x10a3a: 84,
    0x10a3f: 84,
    0x10ac0: 68,
    0x10ac1: 68,
    0x10ac2: 68,
    0x10ac3: 68,
    0x10ac4: 68,
    0x10ac5: 82,
    0x10ac7: 82,
    0x10ac9: 82,
    0x10aca: 82,
    0x10acd: 76,
    0x10ace: 82,
    0x10acf: 82,
    0x10ad0: 82,
    0x10ad1: 82,
    0x10ad2: 82,
    0x10ad3: 68,
    0x10ad4: 68,
    0x10ad5: 68,
    0x10ad6: 68,
    0x10ad7: 76,
    0x10ad8: 68,
    0x10ad9: 68,
    0x10ada: 68,
    0x10adb: 68,
    0x10adc: 68,
    0x10add: 82,
    0x10ade: 68,
    0x10adf: 68,
    0x10ae0: 68,
    0x10ae1: 82,
    0x10ae4: 82,
    0x10ae5: 84,
    0x10ae6: 84,
    0x10aeb: 68,
    0x10aec: 68,
    0x10aed: 68,
    0x10aee: 68,
    0x10aef: 82,
    0x10b80: 68,
    0x10b81: 82,
    0x10b82: 68,
    0x10b83: 82,
    0x10b84: 82,
    0x10b85: 82,
    0x10b86: 68,
    0x10b87: 68,
    0x10b88: 68,
    0x10b89: 82,
    0x10b8a: 68,
    0x10b8b: 68,
    0x10b8c: 82,
    0x10b8d: 68,
    0x10b8e: 82,
    0x10b8f: 82,
    0x10b90: 68,
    0x10b91: 82,
    0x10ba9: 82,
    0x10baa: 82,
    0x10bab: 82,
    0x10bac: 82,
    0x10bad: 68,
    0x10bae: 68,
    0x10d00: 76,
    0x10d01: 68,
    0x10d02: 68,
    0x10d03: 68,
    0x10d04: 68,
    0x10d05: 68,
    0x10d06: 68,
    0x10d07: 68,
    0x10d08: 68,
    0x10d09: 68,
    0x10d0a: 68,
    0x10d0b: 68,
    0x10d0c: 68,
    0x10d0d: 68,
    0x10d0e: 68,
    0x10d0f: 68,
    0x10d10: 68,
    0x10d11: 68,
    0x10d12: 68,
    0x10d13: 68,
    0x10d14: 68,
    0x10d15: 68,
    0x10d16: 68,
    0x10d17: 68,
    0x10d18: 68,
    0x10d19: 68,
    0x10d1a: 68,
    0x10d1b: 68,
    0x10d1c: 68,
    0x10d1d: 68,
    0x10d1e: 68,
    0x10d1f: 68,
    0x10d20: 68,
    0x10d21: 68,
    0x10d22: 82,
    0x10d23: 68,
    0x10d24: 84,
    0x10d25: 84,
    0x10d26: 84,
    0x10d27: 84,
    0x10eab: 84,
    0x10eac: 84,
    0x10efd: 84,
    0x10efe: 84,
    0x10eff: 84,
    0x10f30: 68,
    0x10f31: 68,
    0x10f32: 68,
    0x10f33: 82,
    0x10f34: 68,
    0x10f35: 68,
    0x10f36: 68,
    0x10f37: 68,
    0x10f38: 68,
    0x10f39: 68,
    0x10f3a: 68,
    0x10f3b: 68,
    0x10f3c: 68,
    0x10f3d: 68,
    0x10f3e: 68,
    0x10f3f: 68,
    0x10f40: 68,
    0x10f41: 68,
    0x10f42: 68,
    0x10f43: 68,
    0x10f44: 68,
    0x10f46: 84,
    0x10f47: 84,
    0x10f48: 84,
    0x10f49: 84,
    0x10f4a: 84,
    0x10f4b: 84,
    0x10f4c: 84,
    0x10f4d: 84,
    0x10f4e: 84,
    0x10f4f: 84,
    0x10f50: 84,
    0x10f51: 68,
    0x10f52: 68,
    0x10f53: 68,
    0x10f54: 82,
    0x10f70: 68,
    0x10f71: 68,
    0x10f72: 68,
    0x10f73: 68,
    0x10f74: 82,
    0x10f75: 82,
    0x10f76: 68,
    0x10f77: 68,
    0x10f78: 68,
    0x10f79: 68,
    0x10f7a: 68,
    0x10f7b: 68,
    0x10f7c: 68,
    0x10f7d: 68,
    0x10f7e: 68,
    0x10f7f: 68,
    0x10f80: 68,
    0x10f81: 68,
    0x10f82: 84,
    0x10f83: 84,
    0x10f84: 84,
    0x10f85: 84,
    0x10fb0: 68,
    0x10fb2: 68,
    0x10fb3: 68,
    0x10fb4: 82,
    0x10fb5: 82,
    0x10fb6: 82,
    0x10fb8: 68,
    0x10fb9: 82,
    0x10fba: 82,
    0x10fbb: 68,
    0x10fbc: 68,
    0x10fbd: 82,
    0x10fbe: 68,
    0x10fbf: 68,
    0x10fc1: 68,
    0x10fc2: 82,
    0x10fc3: 82,
    0x10fc4: 68,
    0x10fc9: 82,
    0x10fca: 68,
    0x10fcb: 76,
    0x11001: 84,
    0x11038: 84,
    0x11039: 84,
    0x1103a: 84,
    0x1103b: 84,
    0x1103c: 84,
    0x1103d: 84,
    0x1103e: 84,
    0x1103f: 84,
    0x11040: 84,
    0x11041: 84,
    0x11042: 84,
    0x11043: 84,
    0x11044: 84,
    0x11045: 84,
    0x11046: 84,
    0x11070: 84,
    0x11073: 84,
    0x11074: 84,
    0x1107f: 84,
    0x11080: 84,
    0x11081: 84,
    0x110b3: 84,
    0x110b4: 84,
    0x110b5: 84,
    0x110b6: 84,
    0x110b9: 84,
    0x110ba: 84,
    0x110c2: 84,
    0x11100: 84,
    0x11101: 84,
    0x11102: 84,
    0x11127: 84,
    0x11128: 84,
    0x11129: 84,
    0x1112a: 84,
    0x1112b: 84,
    0x1112d: 84,
    0x1112e: 84,
    0x1112f: 84,
    0x11130: 84,
    0x11131: 84,
    0x11132: 84,
    0x11133: 84,
    0x11134: 84,
    0x11173: 84,
    0x11180: 84,
    0x11181: 84,
    0x111b6: 84,
    0x111b7: 84,
    0x111b8: 84,
    0x111b9: 84,
    0x111ba: 84,
    0x111bb: 84,
    0x111bc: 84,
    0x111bd: 84,
    0x111be: 84,
    0x111c9: 84,
    0x111ca: 84,
    0x111cb: 84,
    0x111cc: 84,
    0x111cf: 84,
    0x1122f: 84,
    0x11230: 84,
    0x11231: 84,
    0x11234: 84,
    0x11236: 84,
    0x11237: 84,
    0x1123e: 84,
    0x11241: 84,
    0x112df: 84,
    0x112e3: 84,
    0x112e4: 84,
    0x112e5: 84,
    0x112e6: 84,
    0x112e7: 84,
    0x112e8: 84,
    0x112e9: 84,
    0x112ea: 84,
    0x11300: 84,
    0x11301: 84,
    0x1133b: 84,
    0x1133c: 84,
    0x11340: 84,
    0x11366: 84,
    0x11367: 84,
    0x11368: 84,
    0x11369: 84,
    0x1136a: 84,
    0x1136b: 84,
    0x1136c: 84,
    0x11370: 84,
    0x11371: 84,
    0x11372: 84,
    0x11373: 84,
    0x11374: 84,
    0x11438: 84,
    0x11439: 84,
    0x1143a: 84,
    0x1143b: 84,
    0x1143c: 84,
    0x1143d: 84,
    0x1143e: 84,
    0x1143f: 84,
    0x11442: 84,
    0x11443: 84,
    0x11444: 84,
    0x11446: 84,
    0x1145e: 84,
    0x114b3: 84,
    0x114b4: 84,
    0x114b5: 84,
    0x114b6: 84,
    0x114b7: 84,
    0x114b8: 84,
    0x114ba: 84,
    0x114bf: 84,
    0x114c0: 84,
    0x114c2: 84,
    0x114c3: 84,
    0x115b2: 84,
    0x115b3: 84,
    0x115b4: 84,
    0x115b5: 84,
    0x115bc: 84,
    0x115bd: 84,
    0x115bf: 84,
    0x115c0: 84,
    0x115dc: 84,
    0x115dd: 84,
    0x11633: 84,
    0x11634: 84,
    0x11635: 84,
    0x11636: 84,
    0x11637: 84,
    0x11638: 84,
    0x11639: 84,
    0x1163a: 84,
    0x1163d: 84,
    0x1163f: 84,
    0x11640: 84,
    0x116ab: 84,
    0x116ad: 84,
    0x116b0: 84,
    0x116b1: 84,
    0x116b2: 84,
    0x116b3: 84,
    0x116b4: 84,
    0x116b5: 84,
    0x116b7: 84,
    0x1171d: 84,
    0x1171e: 84,
    0x1171f: 84,
    0x11722: 84,
    0x11723: 84,
    0x11724: 84,
    0x11725: 84,
    0x11727: 84,
    0x11728: 84,
    0x11729: 84,
    0x1172a: 84,
    0x1172b: 84,
    0x1182f: 84,
    0x11830: 84,
    0x11831: 84,
    0x11832: 84,
    0x11833: 84,
    0x11834: 84,
    0x11835: 84,
    0x11836: 84,
    0x11837: 84,
    0x11839: 84,
    0x1183a: 84,
    0x1193b: 84,
    0x1193c: 84,
    0x1193e: 84,
    0x11943: 84,
    0x119d4: 84,
    0x119d5: 84,
    0x119d6: 84,
    0x119d7: 84,
    0x119da: 84,
    0x119db: 84,
    0x119e0: 84,
    0x11a01: 84,
    0x11a02: 84,
    0x11a03: 84,
    0x11a04: 84,
    0x11a05: 84,
    0x11a06: 84,
    0x11a07: 84,
    0x11a08: 84,
    0x11a09: 84,
    0x11a0a: 84,
    0x11a33: 84,
    0x11a34: 84,
    0x11a35: 84,
    0x11a36: 84,
    0x11a37: 84,
    0x11a38: 84,
    0x11a3b: 84,
    0x11a3c: 84,
    0x11a3d: 84,
    0x11a3e: 84,
    0x11a47: 84,
    0x11a51: 84,
    0x11a52: 84,
    0x11a53: 84,
    0x11a54: 84,
    0x11a55: 84,
    0x11a56: 84,
    0x11a59: 84,
    0x11a5a: 84,
    0x11a5b: 84,
    0x11a8a: 84,
    0x11a8b: 84,
    0x11a8c: 84,
    0x11a8d: 84,
    0x11a8e: 84,
    0x11a8f: 84,
    0x11a90: 84,
    0x11a91: 84,
    0x11a92: 84,
    0x11a93: 84,
    0x11a94: 84,
    0x11a95: 84,
    0x11a96: 84,
    0x11a98: 84,
    0x11a99: 84,
    0x11c30: 84,
    0x11c31: 84,
    0x11c32: 84,
    0x11c33: 84,
    0x11c34: 84,
    0x11c35: 84,
    0x11c36: 84,
    0x11c38: 84,
    0x11c39: 84,
    0x11c3a: 84,
    0x11c3b: 84,
    0x11c3c: 84,
    0x11c3d: 84,
    0x11c3f: 84,
    0x11c92: 84,
    0x11c93: 84,
    0x11c94: 84,
    0x11c95: 84,
    0x11c96: 84,
    0x11c97: 84,
    0x11c98: 84,
    0x11c99: 84,
    0x11c9a: 84,
    0x11c9b: 84,
    0x11c9c: 84,
    0x11c9d: 84,
    0x11c9e: 84,
    0x11c9f: 84,
    0x11ca0: 84,
    0x11ca1: 84,
    0x11ca2: 84,
    0x11ca3: 84,
    0x11ca4: 84,
    0x11ca5: 84,
    0x11ca6: 84,
    0x11ca7: 84,
    0x11caa: 84,
    0x11cab: 84,
    0x11cac: 84,
    0x11cad: 84,
    0x11cae: 84,
    0x11caf: 84,
    0x11cb0: 84,
    0x11cb2: 84,
    0x11cb3: 84,
    0x11cb5: 84,
    0x11cb6: 84,
    0x11d31: 84,
    0x11d32: 84,
    0x11d33: 84,
    0x11d34: 84,
    0x11d35: 84,
    0x11d36: 84,
    0x11d3a: 84,
    0x11d3c: 84,
    0x11d3d: 84,
    0x11d3f: 84,
    0x11d40: 84,
    0x11d41: 84,
    0x11d42: 84,
    0x11d43: 84,
    0x11d44: 84,
    0x11d45: 84,
    0x11d47: 84,
    0x11d90: 84,
    0x11d91: 84,
    0x11d95: 84,
    0x11d97: 84,
    0x11ef3: 84,
    0x11ef4: 84,
    0x11f00: 84,
    0x11f01: 84,
    0x11f36: 84,
    0x11f37: 84,
    0x11f38: 84,
    0x11f39: 84,
    0x11f3a: 84,
    0x11f40: 84,
    0x11f42: 84,
    0x13430: 84,
    0x13431: 84,
    0x13432: 84,
    0x13433: 84,
    0x13434: 84,
    0x13435: 84,
    0x13436: 84,
    0x13437: 84,
    0x13438: 84,
    0x13439: 84,
    0x1343a: 84,
    0x1343b: 84,
    0x1343c: 84,
    0x1343d: 84,
    0x1343e: 84,
    0x1343f: 84,
    0x13440: 84,
    0x13447: 84,
    0x13448: 84,
    0x13449: 84,
    0x1344a: 84,
    0x1344b: 84,
    0x1344c: 84,
    0x1344d: 84,
    0x1344e: 84,
    0x1344f: 84,
    0x13450: 84,
    0x13451: 84,
    0x13452: 84,
    0x13453: 84,
    0x13454: 84,
    0x13455: 84,
    0x16af0: 84,
    0x16af1: 84,
    0x16af2: 84,
    0x16af3: 84,
    0x16af4: 84,
    0x16b30: 84,
    0x16b31: 84,
    0x16b32: 84,
    0x16b33: 84,
    0x16b34: 84,
    0x16b35: 84,
    0x16b36: 84,
    0x16f4f: 84,
    0x16f8f: 84,
    0x16f90: 84,
    0x16f91: 84,
    0x16f92: 84,
    0x16fe4: 84,
    0x1bc9d: 84,
    0x1bc9e: 84,
    0x1bca0: 84,
    0x1bca1: 84,
    0x1bca2: 84,
    0x1bca3: 84,
    0x1cf00: 84,
    0x1cf01: 84,
    0x1cf02: 84,
    0x1cf03: 84,
    0x1cf04: 84,
    0x1cf05: 84,
    0x1cf06: 84,
    0x1cf07: 84,
    0x1cf08: 84,
    0x1cf09: 84,
    0x1cf0a: 84,
    0x1cf0b: 84,
    0x1cf0c: 84,
    0x1cf0d: 84,
    0x1cf0e: 84,
    0x1cf0f: 84,
    0x1cf10: 84,
    0x1cf11: 84,
    0x1cf12: 84,
    0x1cf13: 84,
    0x1cf14: 84,
    0x1cf15: 84,
    0x1cf16: 84,
    0x1cf17: 84,
    0x1cf18: 84,
    0x1cf19: 84,
    0x1cf1a: 84,
    0x1cf1b: 84,
    0x1cf1c: 84,
    0x1cf1d: 84,
    0x1cf1e: 84,
    0x1cf1f: 84,
    0x1cf20: 84,
    0x1cf21: 84,
    0x1cf22: 84,
    0x1cf23: 84,
    0x1cf24: 84,
    0x1cf25: 84,
    0x1cf26: 84,
    0x1cf27: 84,
    0x1cf28: 84,
    0x1cf29: 84,
    0x1cf2a: 84,
    0x1cf2b: 84,
    0x1cf2c: 84,
    0x1cf2d: 84,
    0x1cf30: 84,
    0x1cf31: 84,
    0x1cf32: 84,
    0x1cf33: 84,
    0x1cf34: 84,
    0x1cf35: 84,
    0x1cf36: 84,
    0x1cf37: 84,
    0x1cf38: 84,
    0x1cf39: 84,
    0x1cf3a: 84,
    0x1cf3b: 84,
    0x1cf3c: 84,
    0x1cf3d: 84,
    0x1cf3e: 84,
    0x1cf3f: 84,
    0x1cf40: 84,
    0x1cf41: 84,
    0x1cf42: 84,
    0x1cf43: 84,
    0x1cf44: 84,
    0x1cf45: 84,
    0x1cf46: 84,
    0x1d167: 84,
    0x1d168: 84,
    0x1d169: 84,
    0x1d173: 84,
    0x1d174: 84,
    0x1d175: 84,
    0x1d176: 84,
    0x1d177: 84,
    0x1d178: 84,
    0x1d179: 84,
    0x1d17a: 84,
    0x1d17b: 84,
    0x1d17c: 84,
    0x1d17d: 84,
    0x1d17e: 84,
    0x1d17f: 84,
    0x1d180: 84,
    0x1d181: 84,
    0x1d182: 84,
    0x1d185: 84,
    0x1d186: 84,
    0x1d187: 84,
    0x1d188: 84,
    0x1d189: 84,
    0x1d18a: 84,
    0x1d18b: 84,
    0x1d1aa: 84,
    0x1d1ab: 84,
    0x1d1ac: 84,
    0x1d1ad: 84,
    0x1d242: 84,
    0x1d243: 84,
    0x1d244: 84,
    0x1da00: 84,
    0x1da01: 84,
    0x1da02: 84,
    0x1da03: 84,
    0x1da04: 84,
    0x1da05: 84,
    0x1da06: 84,
    0x1da07: 84,
    0x1da08: 84,
    0x1da09: 84,
    0x1da0a: 84,
    0x1da0b: 84,
    0x1da0c: 84,
    0x1da0d: 84,
    0x1da0e: 84,
    0x1da0f: 84,
    0x1da10: 84,
    0x1da11: 84,
    0x1da12: 84,
    0x1da13: 84,
    0x1da14: 84,
    0x1da15: 84,
    0x1da16: 84,
    0x1da17: 84,
    0x1da18: 84,
    0x1da19: 84,
    0x1da1a: 84,
    0x1da1b: 84,
    0x1da1c: 84,
    0x1da1d: 84,
    0x1da1e: 84,
    0x1da1f: 84,
    0x1da20: 84,
    0x1da21: 84,
    0x1da22: 84,
    0x1da23: 84,
    0x1da24: 84,
    0x1da25: 84,
    0x1da26: 84,
    0x1da27: 84,
    0x1da28: 84,
    0x1da29: 84,
    0x1da2a: 84,
    0x1da2b: 84,
    0x1da2c: 84,
    0x1da2d: 84,
    0x1da2e: 84,
    0x1da2f: 84,
    0x1da30: 84,
    0x1da31: 84,
    0x1da32: 84,
    0x1da33: 84,
    0x1da34: 84,
    0x1da35: 84,
    0x1da36: 84,
    0x1da3b: 84,
    0x1da3c: 84,
    0x1da3d: 84,
    0x1da3e: 84,
    0x1da3f: 84,
    0x1da40: 84,
    0x1da41: 84,
    0x1da42: 84,
    0x1da43: 84,
    0x1da44: 84,
    0x1da45: 84,
    0x1da46: 84,
    0x1da47: 84,
    0x1da48: 84,
    0x1da49: 84,
    0x1da4a: 84,
    0x1da4b: 84,
    0x1da4c: 84,
    0x1da4d: 84,
    0x1da4e: 84,
    0x1da4f: 84,
    0x1da50: 84,
    0x1da51: 84,
    0x1da52: 84,
    0x1da53: 84,
    0x1da54: 84,
    0x1da55: 84,
    0x1da56: 84,
    0x1da57: 84,
    0x1da58: 84,
    0x1da59: 84,
    0x1da5a: 84,
    0x1da5b: 84,
    0x1da5c: 84,
    0x1da5d: 84,
    0x1da5e: 84,
    0x1da5f: 84,
    0x1da60: 84,
    0x1da61: 84,
    0x1da62: 84,
    0x1da63: 84,
    0x1da64: 84,
    0x1da65: 84,
    0x1da66: 84,
    0x1da67: 84,
    0x1da68: 84,
    0x1da69: 84,
    0x1da6a: 84,
    0x1da6b: 84,
    0x1da6c: 84,
    0x1da75: 84,
    0x1da84: 84,
    0x1da9b: 84,
    0x1da9c: 84,
    0x1da9d: 84,
    0x1da9e: 84,
    0x1da9f: 84,
    0x1daa1: 84,
    0x1daa2: 84,
    0x1daa3: 84,
    0x1daa4: 84,
    0x1daa5: 84,
    0x1daa6: 84,
    0x1daa7: 84,
    0x1daa8: 84,
    0x1daa9: 84,
    0x1daaa: 84,
    0x1daab: 84,
    0x1daac: 84,
    0x1daad: 84,
    0x1daae: 84,
    0x1daaf: 84,
    0x1e000: 84,
    0x1e001: 84,
    0x1e002: 84,
    0x1e003: 84,
    0x1e004: 84,
    0x1e005: 84,
    0x1e006: 84,
    0x1e008: 84,
    0x1e009: 84,
    0x1e00a: 84,
    0x1e00b: 84,
    0x1e00c: 84,
    0x1e00d: 84,
    0x1e00e: 84,
    0x1e00f: 84,
    0x1e010: 84,
    0x1e011: 84,
    0x1e012: 84,
    0x1e013: 84,
    0x1e014: 84,
    0x1e015: 84,
    0x1e016: 84,
    0x1e017: 84,
    0x1e018: 84,
    0x1e01b: 84,
    0x1e01c: 84,
    0x1e01d: 84,
    0x1e01e: 84,
    0x1e01f: 84,
    0x1e020: 84,
    0x1e021: 84,
    0x1e023: 84,
    0x1e024: 84,
    0x1e026: 84,
    0x1e027: 84,
    0x1e028: 84,
    0x1e029: 84,
    0x1e02a: 84,
    0x1e08f: 84,
    0x1e130: 84,
    0x1e131: 84,
    0x1e132: 84,
    0x1e133: 84,
    0x1e134: 84,
    0x1e135: 84,
    0x1e136: 84,
    0x1e2ae: 84,
    0x1e2ec: 84,
    0x1e2ed: 84,
    0x1e2ee: 84,
    0x1e2ef: 84,
    0x1e4ec: 84,
    0x1e4ed: 84,
    0x1e4ee: 84,
    0x1e4ef: 84,
    0x1e8d0: 84,
    0x1e8d1: 84,
    0x1e8d2: 84,
    0x1e8d3: 84,
    0x1e8d4: 84,
    0x1e8d5: 84,
    0x1e8d6: 84,
    0x1e900: 68,
    0x1e901: 68,
    0x1e902: 68,
    0x1e903: 68,
    0x1e904: 68,
    0x1e905: 68,
    0x1e906: 68,
    0x1e907: 68,
    0x1e908: 68,
    0x1e909: 68,
    0x1e90a: 68,
    0x1e90b: 68,
    0x1e90c: 68,
    0x1e90d: 68,
    0x1e90e: 68,
    0x1e90f: 68,
    0x1e910: 68,
    0x1e911: 68,
    0x1e912: 68,
    0x1e913: 68,
    0x1e914: 68,
    0x1e915: 68,
    0x1e916: 68,
    0x1e917: 68,
    0x1e918: 68,
    0x1e919: 68,
    0x1e91a: 68,
    0x1e91b: 68,
    0x1e91c: 68,
    0x1e91d: 68,
    0x1e91e: 68,
    0x1e91f: 68,
    0x1e920: 68,
    0x1e921: 68,
    0x1e922: 68,
    0x1e923: 68,
    0x1e924: 68,
    0x1e925: 68,
    0x1e926: 68,
    0x1e927: 68,
    0x1e928: 68,
    0x1e929: 68,
    0x1e92a: 68,
    0x1e92b: 68,
    0x1e92c: 68,
    0x1e92d: 68,
    0x1e92e: 68,
    0x1e92f: 68,
    0x1e930: 68,
    0x1e931: 68,
    0x1e932: 68,
    0x1e933: 68,
    0x1e934: 68,
    0x1e935: 68,
    0x1e936: 68,
    0x1e937: 68,
    0x1e938: 68,
    0x1e939: 68,
    0x1e93a: 68,
    0x1e93b: 68,
    0x1e93c: 68,
    0x1e93d: 68,
    0x1e93e: 68,
    0x1e93f: 68,
    0x1e940: 68,
    0x1e941: 68,
    0x1e942: 68,
    0x1e943: 68,
    0x1e944: 84,
    0x1e945: 84,
    0x1e946: 84,
    0x1e947: 84,
    0x1e948: 84,
    0x1e949: 84,
    0x1e94a: 84,
    0x1e94b: 84,
    0xe0001: 84,
    0xe0020: 84,
    0xe0021: 84,
    0xe0022: 84,
    0xe0023: 84,
    0xe0024: 84,
    0xe0025: 84,
    0xe0026: 84,
    0xe0027: 84,
    0xe0028: 84,
    0xe0029: 84,
    0xe002a: 84,
    0xe002b: 84,
    0xe002c: 84,
    0xe002d: 84,
    0xe002e: 84,
    0xe002f: 84,
    0xe0030: 84,
    0xe0031: 84,
    0xe0032: 84,
    0xe0033: 84,
    0xe0034: 84,
    0xe0035: 84,
    0xe0036: 84,
    0xe0037: 84,
    0xe0038: 84,
    0xe0039: 84,
    0xe003a: 84,
    0xe003b: 84,
    0xe003c: 84,
    0xe003d: 84,
    0xe003e: 84,
    0xe003f: 84,
    0xe0040: 84,
    0xe0041: 84,
    0xe0042: 84,
    0xe0043: 84,
    0xe0044: 84,
    0xe0045: 84,
    0xe0046: 84,
    0xe0047: 84,
    0xe0048: 84,
    0xe0049: 84,
    0xe004a: 84,
    0xe004b: 84,
    0xe004c: 84,
    0xe004d: 84,
    0xe004e: 84,
    0xe004f: 84,
    0xe0050: 84,
    0xe0051: 84,
    0xe0052: 84,
    0xe0053: 84,
    0xe0054: 84,
    0xe0055: 84,
    0xe0056: 84,
    0xe0057: 84,
    0xe0058: 84,
    0xe0059: 84,
    0xe005a: 84,
    0xe005b: 84,
    0xe005c: 84,
    0xe005d: 84,
    0xe005e: 84,
    0xe005f: 84,
    0xe0060: 84,
    0xe0061: 84,
    0xe0062: 84,
    0xe0063: 84,
    0xe0064: 84,
    0xe0065: 84,
    0xe0066: 84,
    0xe0067: 84,
    0xe0068: 84,
    0xe0069: 84,
    0xe006a: 84,
    0xe006b: 84,
    0xe006c: 84,
    0xe006d: 84,
    0xe006e: 84,
    0xe006f: 84,
    0xe0070: 84,
    0xe0071: 84,
    0xe0072: 84,
    0xe0073: 84,
    0xe0074: 84,
    0xe0075: 84,
    0xe0076: 84,
    0xe0077: 84,
    0xe0078: 84,
    0xe0079: 84,
    0xe007a: 84,
    0xe007b: 84,
    0xe007c: 84,
    0xe007d: 84,
    0xe007e: 84,
    0xe007f: 84,
    0xe0100: 84,
    0xe0101: 84,
    0xe0102: 84,
    0xe0103: 84,
    0xe0104: 84,
    0xe0105: 84,
    0xe0106: 84,
    0xe0107: 84,
    0xe0108: 84,
    0xe0109: 84,
    0xe010a: 84,
    0xe010b: 84,
    0xe010c: 84,
    0xe010d: 84,
    0xe010e: 84,
    0xe010f: 84,
    0xe0110: 84,
    0xe0111: 84,
    0xe0112: 84,
    0xe0113: 84,
    0xe0114: 84,
    0xe0115: 84,
    0xe0116: 84,
    0xe0117: 84,
    0xe0118: 84,
    0xe0119: 84,
    0xe011a: 84,
    0xe011b: 84,
    0xe011c: 84,
    0xe011d: 84,
    0xe011e: 84,
    0xe011f: 84,
    0xe0120: 84,
    0xe0121: 84,
    0xe0122: 84,
    0xe0123: 84,
    0xe0124: 84,
    0xe0125: 84,
    0xe0126: 84,
    0xe0127: 84,
    0xe0128: 84,
    0xe0129: 84,
    0xe012a: 84,
    0xe012b: 84,
    0xe012c: 84,
    0xe012d: 84,
    0xe012e: 84,
    0xe012f: 84,
    0xe0130: 84,
    0xe0131: 84,
    0xe0132: 84,
    0xe0133: 84,
    0xe0134: 84,
    0xe0135: 84,
    0xe0136: 84,
    0xe0137: 84,
    0xe0138: 84,
    0xe0139: 84,
    0xe013a: 84,
    0xe013b: 84,
    0xe013c: 84,
    0xe013d: 84,
    0xe013e: 84,
    0xe013f: 84,
    0xe0140: 84,
    0xe0141: 84,
    0xe0142: 84,
    0xe0143: 84,
    0xe0144: 84,
    0xe0145: 84,
    0xe0146: 84,
    0xe0147: 84,
    0xe0148: 84,
    0xe0149: 84,
    0xe014a: 84,
    0xe014b: 84,
    0xe014c: 84,
    0xe014d: 84,
    0xe014e: 84,
    0xe014f: 84,
    0xe0150: 84,
    0xe0151: 84,
    0xe0152: 84,
    0xe0153: 84,
    0xe0154: 84,
    0xe0155: 84,
    0xe0156: 84,
    0xe0157: 84,
    0xe0158: 84,
    0xe0159: 84,
    0xe015a: 84,
    0xe015b: 84,
    0xe015c: 84,
    0xe015d: 84,
    0xe015e: 84,
    0xe015f: 84,
    0xe0160: 84,
    0xe0161: 84,
    0xe0162: 84,
    0xe0163: 84,
    0xe0164: 84,
    0xe0165: 84,
    0xe0166: 84,
    0xe0167: 84,
    0xe0168: 84,
    0xe0169: 84,
    0xe016a: 84,
    0xe016b: 84,
    0xe016c: 84,
    0xe016d: 84,
    0xe016e: 84,
    0xe016f: 84,
    0xe0170: 84,
    0xe0171: 84,
    0xe0172: 84,
    0xe0173: 84,
    0xe0174: 84,
    0xe0175: 84,
    0xe0176: 84,
    0xe0177: 84,
    0xe0178: 84,
    0xe0179: 84,
    0xe017a: 84,
    0xe017b: 84,
    0xe017c: 84,
    0xe017d: 84,
    0xe017e: 84,
    0xe017f: 84,
    0xe0180: 84,
    0xe0181: 84,
    0xe0182: 84,
    0xe0183: 84,
    0xe0184: 84,
    0xe0185: 84,
    0xe0186: 84,
    0xe0187: 84,
    0xe0188: 84,
    0xe0189: 84,
    0xe018a: 84,
    0xe018b: 84,
    0xe018c: 84,
    0xe018d: 84,
    0xe018e: 84,
    0xe018f: 84,
    0xe0190: 84,
    0xe0191: 84,
    0xe0192: 84,
    0xe0193: 84,
    0xe0194: 84,
    0xe0195: 84,
    0xe0196: 84,
    0xe0197: 84,
    0xe0198: 84,
    0xe0199: 84,
    0xe019a: 84,
    0xe019b: 84,
    0xe019c: 84,
    0xe019d: 84,
    0xe019e: 84,
    0xe019f: 84,
    0xe01a0: 84,
    0xe01a1: 84,
    0xe01a2: 84,
    0xe01a3: 84,
    0xe01a4: 84,
    0xe01a5: 84,
    0xe01a6: 84,
    0xe01a7: 84,
    0xe01a8: 84,
    0xe01a9: 84,
    0xe01aa: 84,
    0xe01ab: 84,
    0xe01ac: 84,
    0xe01ad: 84,
    0xe01ae: 84,
    0xe01af: 84,
    0xe01b0: 84,
    0xe01b1: 84,
    0xe01b2: 84,
    0xe01b3: 84,
    0xe01b4: 84,
    0xe01b5: 84,
    0xe01b6: 84,
    0xe01b7: 84,
    0xe01b8: 84,
    0xe01b9: 84,
    0xe01ba: 84,
    0xe01bb: 84,
    0xe01bc: 84,
    0xe01bd: 84,
    0xe01be: 84,
    0xe01bf: 84,
    0xe01c0: 84,
    0xe01c1: 84,
    0xe01c2: 84,
    0xe01c3: 84,
    0xe01c4: 84,
    0xe01c5: 84,
    0xe01c6: 84,
    0xe01c7: 84,
    0xe01c8: 84,
    0xe01c9: 84,
    0xe01ca: 84,
    0xe01cb: 84,
    0xe01cc: 84,
    0xe01cd: 84,
    0xe01ce: 84,
    0xe01cf: 84,
    0xe01d0: 84,
    0xe01d1: 84,
    0xe01d2: 84,
    0xe01d3: 84,
    0xe01d4: 84,
    0xe01d5: 84,
    0xe01d6: 84,
    0xe01d7: 84,
    0xe01d8: 84,
    0xe01d9: 84,
    0xe01da: 84,
    0xe01db: 84,
    0xe01dc: 84,
    0xe01dd: 84,
    0xe01de: 84,
    0xe01df: 84,
    0xe01e0: 84,
    0xe01e1: 84,
    0xe01e2: 84,
    0xe01e3: 84,
    0xe01e4: 84,
    0xe01e5: 84,
    0xe01e6: 84,
    0xe01e7: 84,
    0xe01e8: 84,
    0xe01e9: 84,
    0xe01ea: 84,
    0xe01eb: 84,
    0xe01ec: 84,
    0xe01ed: 84,
    0xe01ee: 84,
    0xe01ef: 84,
}
codepoint_classes = {
    'PVALID': (
        0x2d0000002e,
        0x300000003a,
        0x610000007b,
        0xdf000000f7,
        0xf800000100,
        0x10100000102,
        0x10300000104,
        0x10500000106,
        0x10700000108,
        0x1090000010a,
        0x10b0000010c,
        0x10d0000010e,
        0x10f00000110,
        0x11100000112,
        0x11300000114,
        0x11500000116,
        0x11700000118,
        0x1190000011a,
        0x11b0000011c,
        0x11d0000011e,
        0x11f00000120,
        0x12100000122,
        0x12300000124,
        0x12500000126,
        0x12700000128,
        0x1290000012a,
        0x12b0000012c,
        0x12d0000012e,
        0x12f00000130,
        0x13100000132,
        0x13500000136,
        0x13700000139,
        0x13a0000013b,
        0x13c0000013d,
        0x13e0000013f,
        0x14200000143,
        0x14400000145,
        0x14600000147,
        0x14800000149,
        0x14b0000014c,
        0x14d0000014e,
        0x14f00000150,
        0x15100000152,
        0x15300000154,
        0x15500000156,
        0x15700000158,
        0x1590000015a,
        0x15b0000015c,
        0x15d0000015e,
        0x15f00000160,
        0x16100000162,
        0x16300000164,
        0x16500000166,
        0x16700000168,
        0x1690000016a,
        0x16b0000016c,
        0x16d0000016e,
        0x16f00000170,
        0x17100000172,
        0x17300000174,
        0x17500000176,
        0x17700000178,
        0x17a0000017b,
        0x17c0000017d,
        0x17e0000017f,
        0x18000000181,
        0x18300000184,
        0x18500000186,
        0x18800000189,
        0x18c0000018e,
        0x19200000193,
        0x19500000196,
        0x1990000019c,
        0x19e0000019f,
        0x1a1000001a2,
        0x1a3000001a4,
        0x1a5000001a6,
        0x1a8000001a9,
        0x1aa000001ac,
        0x1ad000001ae,
        0x1b0000001b1,
        0x1b4000001b5,
        0x1b6000001b7,
        0x1b9000001bc,
        0x1bd000001c4,
        0x1ce000001cf,
        0x1d0000001d1,
        0x1d2000001d3,
        0x1d4000001d5,
        0x1d6000001d7,
        0x1d8000001d9,
        0x1da000001db,
        0x1dc000001de,
        0x1df000001e0,
        0x1e1000001e2,
        0x1e3000001e4,
        0x1e5000001e6,
        0x1e7000001e8,
        0x1e9000001ea,
        0x1eb000001ec,
        0x1ed000001ee,
        0x1ef000001f1,
        0x1f5000001f6,
        0x1f9000001fa,
        0x1fb000001fc,
        0x1fd000001fe,
        0x1ff00000200,
        0x20100000202,
        0x20300000204,
        0x20500000206,
        0x20700000208,
        0x2090000020a,
        0x20b0000020c,
        0x20d0000020e,
        0x20f00000210,
        0x21100000212,
        0x21300000214,
        0x21500000216,
        0x21700000218,
        0x2190000021a,
        0x21b0000021c,
        0x21d0000021e,
        0x21f00000220,
        0x22100000222,
        0x22300000224,
        0x22500000226,
        0x22700000228,
        0x2290000022a,
        0x22b0000022c,
        0x22d0000022e,
        0x22f00000230,
        0x23100000232,
        0x2330000023a,
        0x23c0000023d,
        0x23f00000241,
        0x24200000243,
        0x24700000248,
        0x2490000024a,
        0x24b0000024c,
        0x24d0000024e,
        0x24f000002b0,
        0x2b9000002c2,
        0x2c6000002d2,
        0x2ec000002ed,
        0x2ee000002ef,
        0x30000000340,
        0x34200000343,
        0x3460000034f,
        0x35000000370,
        0x37100000372,
        0x37300000374,
        0x37700000378,
        0x37b0000037e,
        0x39000000391,
        0x3ac000003cf,
        0x3d7000003d8,
        0x3d9000003da,
        0x3db000003dc,
        0x3dd000003de,
        0x3df000003e0,
        0x3e1000003e2,
        0x3e3000003e4,
        0x3e5000003e6,
        0x3e7000003e8,
        0x3e9000003ea,
        0x3eb000003ec,
        0x3ed000003ee,
        0x3ef000003f0,
        0x3f3000003f4,
        0x3f8000003f9,
        0x3fb000003fd,
        0x43000000460,
        0x46100000462,
        0x46300000464,
        0x46500000466,
        0x46700000468,
        0x4690000046a,
        0x46b0000046c,
        0x46d0000046e,
        0x46f00000470,
        0x47100000472,
        0x47300000474,
        0x47500000476,
        0x47700000478,
        0x4790000047a,
        0x47b0000047c,
        0x47d0000047e,
        0x47f00000480,
        0x48100000482,
        0x48300000488,
        0x48b0000048c,
        0x48d0000048e,
        0x48f00000490,
        0x49100000492,
        0x49300000494,
        0x49500000496,
        0x49700000498,
        0x4990000049a,
        0x49b0000049c,
        0x49d0000049e,
        0x49f000004a0,
        0x4a1000004a2,
        0x4a3000004a4,
        0x4a5000004a6,
        0x4a7000004a8,
        0x4a9000004aa,
        0x4ab000004ac,
        0x4ad000004ae,
        0x4af000004b0,
        0x4b1000004b2,
        0x4b3000004b4,
        0x4b5000004b6,
        0x4b7000004b8,
        0x4b9000004ba,
        0x4bb000004bc,
        0x4bd000004be,
        0x4bf000004c0,
        0x4c2000004c3,
        0x4c4000004c5,
        0x4c6000004c7,
        0x4c8000004c9,
        0x4ca000004cb,
        0x4cc000004cd,
        0x4ce000004d0,
        0x4d1000004d2,
        0x4d3000004d4,
        0x4d5000004d6,
        0x4d7000004d8,
        0x4d9000004da,
        0x4db000004dc,
        0x4dd000004de,
        0x4df000004e0,
        0x4e1000004e2,
        0x4e3000004e4,
        0x4e5000004e6,
        0x4e7000004e8,
        0x4e9000004ea,
        0x4eb000004ec,
        0x4ed000004ee,
        0x4ef000004f0,
        0x4f1000004f2,
        0x4f3000004f4,
        0x4f5000004f6,
        0x4f7000004f8,
        0x4f9000004fa,
        0x4fb000004fc,
        0x4fd000004fe,
        0x4ff00000500,
        0x50100000502,
        0x50300000504,
        0x50500000506,
        0x50700000508,
        0x5090000050a,
        0x50b0000050c,
        0x50d0000050e,
        0x50f00000510,
        0x51100000512,
        0x51300000514,
        0x51500000516,
        0x51700000518,
        0x5190000051a,
        0x51b0000051c,
        0x51d0000051e,
        0x51f00000520,
        0x52100000522,
        0x52300000524,
        0x52500000526,
        0x52700000528,
        0x5290000052a,
        0x52b0000052c,
        0x52d0000052e,
        0x52f00000530,
        0x5590000055a,
        0x56000000587,
        0x58800000589,
        0x591000005be,
        0x5bf000005c0,
        0x5c1000005c3,
        0x5c4000005c6,
        0x5c7000005c8,
        0x5d0000005eb,
        0x5ef000005f3,
        0x6100000061b,
        0x62000000640,
        0x64100000660,
        0x66e00000675,
        0x679000006d4,
        0x6d5000006dd,
        0x6df000006e9,
        0x6ea000006f0,
        0x6fa00000700,
        0x7100000074b,
        0x74d000007b2,
        0x7c0000007f6,
        0x7fd000007fe,
        0x8000000082e,
        0x8400000085c,
        0x8600000086b,
        0x87000000888,
        0x8890000088f,
        0x898000008e2,
        0x8e300000958,
        0x96000000964,
        0x96600000970,
        0x97100000984,
        0x9850000098d,
        0x98f00000991,
        0x993000009a9,
        0x9aa000009b1,
        0x9b2000009b3,
        0x9b6000009ba,
        0x9bc000009c5,
        0x9c7000009c9,
        0x9cb000009cf,
        0x9d7000009d8,
        0x9e0000009e4,
        0x9e6000009f2,
        0x9fc000009fd,
        0x9fe000009ff,
        0xa0100000a04,
        0xa0500000a0b,
        0xa0f00000a11,
        0xa1300000a29,
        0xa2a00000a31,
        0xa3200000a33,
        0xa3500000a36,
        0xa3800000a3a,
        0xa3c00000a3d,
        0xa3e00000a43,
        0xa4700000a49,
        0xa4b00000a4e,
        0xa5100000a52,
        0xa5c00000a5d,
        0xa6600000a76,
        0xa8100000a84,
        0xa8500000a8e,
        0xa8f00000a92,
        0xa9300000aa9,
        0xaaa00000ab1,
        0xab200000ab4,
        0xab500000aba,
        0xabc00000ac6,
        0xac700000aca,
        0xacb00000ace,
        0xad000000ad1,
        0xae000000ae4,
        0xae600000af0,
        0xaf900000b00,
        0xb0100000b04,
        0xb0500000b0d,
        0xb0f00000b11,
        0xb1300000b29,
        0xb2a00000b31,
        0xb3200000b34,
        0xb3500000b3a,
        0xb3c00000b45,
        0xb4700000b49,
        0xb4b00000b4e,
        0xb5500000b58,
        0xb5f00000b64,
        0xb6600000b70,
        0xb7100000b72,
        0xb8200000b84,
        0xb8500000b8b,
        0xb8e00000b91,
        0xb9200000b96,
        0xb9900000b9b,
        0xb9c00000b9d,
        0xb9e00000ba0,
        0xba300000ba5,
        0xba800000bab,
        0xbae00000bba,
        0xbbe00000bc3,
        0xbc600000bc9,
        0xbca00000bce,
        0xbd000000bd1,
        0xbd700000bd8,
        0xbe600000bf0,
        0xc0000000c0d,
        0xc0e00000c11,
        0xc1200000c29,
        0xc2a00000c3a,
        0xc3c00000c45,
        0xc4600000c49,
        0xc4a00000c4e,
        0xc5500000c57,
        0xc5800000c5b,
        0xc5d00000c5e,
        0xc6000000c64,
        0xc6600000c70,
        0xc8000000c84,
        0xc8500000c8d,
        0xc8e00000c91,
        0xc9200000ca9,
        0xcaa00000cb4,
        0xcb500000cba,
        0xcbc00000cc5,
        0xcc600000cc9,
        0xcca00000cce,
        0xcd500000cd7,
        0xcdd00000cdf,
        0xce000000ce4,
        0xce600000cf0,
        0xcf100000cf4,
        0xd0000000d0d,
        0xd0e00000d11,
        0xd1200000d45,
        0xd4600000d49,
        0xd4a00000d4f,
        0xd5400000d58,
        0xd5f00000d64,
        0xd6600000d70,
        0xd7a00000d80,
        0xd8100000d84,
        0xd8500000d97,
        0xd9a00000db2,
        0xdb300000dbc,
        0xdbd00000dbe,
        0xdc000000dc7,
        0xdca00000dcb,
        0xdcf00000dd5,
        0xdd600000dd7,
        0xdd800000de0,
        0xde600000df0,
        0xdf200000df4,
        0xe0100000e33,
        0xe3400000e3b,
        0xe4000000e4f,
        0xe5000000e5a,
        0xe8100000e83,
        0xe8400000e85,
        0xe8600000e8b,
        0xe8c00000ea4,
        0xea500000ea6,
        0xea700000eb3,
        0xeb400000ebe,
        0xec000000ec5,
        0xec600000ec7,
        0xec800000ecf,
        0xed000000eda,
        0xede00000ee0,
        0xf0000000f01,
        0xf0b00000f0c,
        0xf1800000f1a,
        0xf2000000f2a,
        0xf3500000f36,
        0xf3700000f38,
        0xf3900000f3a,
        0xf3e00000f43,
        0xf4400000f48,
        0xf4900000f4d,
        0xf4e00000f52,
        0xf5300000f57,
        0xf5800000f5c,
        0xf5d00000f69,
        0xf6a00000f6d,
        0xf7100000f73,
        0xf7400000f75,
        0xf7a00000f81,
        0xf8200000f85,
        0xf8600000f93,
        0xf9400000f98,
        0xf9900000f9d,
        0xf9e00000fa2,
        0xfa300000fa7,
        0xfa800000fac,
        0xfad00000fb9,
        0xfba00000fbd,
        0xfc600000fc7,
        0x10000000104a,
        0x10500000109e,
        0x10d0000010fb,
        0x10fd00001100,
        0x120000001249,
        0x124a0000124e,
        0x125000001257,
        0x125800001259,
        0x125a0000125e,
        0x126000001289,
        0x128a0000128e,
        0x1290000012b1,
        0x12b2000012b6,
        0x12b8000012bf,
        0x12c0000012c1,
        0x12c2000012c6,
        0x12c8000012d7,
        0x12d800001311,
        0x131200001316,
        0x13180000135b,
        0x135d00001360,
        0x138000001390,
        0x13a0000013f6,
        0x14010000166d,
        0x166f00001680,
        0x16810000169b,
        0x16a0000016eb,
        0x16f1000016f9,
        0x170000001716,
        0x171f00001735,
        0x174000001754,
        0x17600000176d,
        0x176e00001771,
        0x177200001774,
        0x1780000017b4,
        0x17b6000017d4,
        0x17d7000017d8,
        0x17dc000017de,
        0x17e0000017ea,
        0x18100000181a,
        0x182000001879,
        0x1880000018ab,
        0x18b0000018f6,
        0x19000000191f,
        0x19200000192c,
        0x19300000193c,
        0x19460000196e,
        0x197000001975,
        0x1980000019ac,
        0x19b0000019ca,
        0x19d0000019da,
        0x1a0000001a1c,
        0x1a2000001a5f,
        0x1a6000001a7d,
        0x1a7f00001a8a,
        0x1a9000001a9a,
        0x1aa700001aa8,
        0x1ab000001abe,
        0x1abf00001acf,
        0x1b0000001b4d,
        0x1b5000001b5a,
        0x1b6b00001b74,
        0x1b8000001bf4,
        0x1c0000001c38,
        0x1c4000001c4a,
        0x1c4d00001c7e,
        0x1cd000001cd3,
        0x1cd400001cfb,
        0x1d0000001d2c,
        0x1d2f00001d30,
        0x1d3b00001d3c,
        0x1d4e00001d4f,
        0x1d6b00001d78,
        0x1d7900001d9b,
        0x1dc000001e00,
        0x1e0100001e02,
        0x1e0300001e04,
        0x1e0500001e06,
        0x1e0700001e08,
        0x1e0900001e0a,
        0x1e0b00001e0c,
        0x1e0d00001e0e,
        0x1e0f00001e10,
        0x1e1100001e12,
        0x1e1300001e14,
        0x1e1500001e16,
        0x1e1700001e18,
        0x1e1900001e1a,
        0x1e1b00001e1c,
        0x1e1d00001e1e,
        0x1e1f00001e20,
        0x1e2100001e22,
        0x1e2300001e24,
        0x1e2500001e26,
        0x1e2700001e28,
        0x1e2900001e2a,
        0x1e2b00001e2c,
        0x1e2d00001e2e,
        0x1e2f00001e30,
        0x1e3100001e32,
        0x1e3300001e34,
        0x1e3500001e36,
        0x1e3700001e38,
        0x1e3900001e3a,
        0x1e3b00001e3c,
        0x1e3d00001e3e,
        0x1e3f00001e40,
        0x1e4100001e42,
        0x1e4300001e44,
        0x1e4500001e46,
        0x1e4700001e48,
        0x1e4900001e4a,
        0x1e4b00001e4c,
        0x1e4d00001e4e,
        0x1e4f00001e50,
        0x1e5100001e52,
        0x1e5300001e54,
        0x1e5500001e56,
        0x1e5700001e58,
        0x1e5900001e5a,
        0x1e5b00001e5c,
        0x1e5d00001e5e,
        0x1e5f00001e60,
        0x1e6100001e62,
        0x1e6300001e64,
        0x1e6500001e66,
        0x1e6700001e68,
        0x1e6900001e6a,
        0x1e6b00001e6c,
        0x1e6d00001e6e,
        0x1e6f00001e70,
        0x1e7100001e72,
        0x1e7300001e74,
        0x1e7500001e76,
        0x1e7700001e78,
        0x1e7900001e7a,
        0x1e7b00001e7c,
        0x1e7d00001e7e,
        0x1e7f00001e80,
        0x1e8100001e82,
        0x1e8300001e84,
        0x1e8500001e86,
        0x1e8700001e88,
        0x1e8900001e8a,
        0x1e8b00001e8c,
        0x1e8d00001e8e,
        0x1e8f00001e90,
        0x1e9100001e92,
        0x1e9300001e94,
        0x1e9500001e9a,
        0x1e9c00001e9e,
        0x1e9f00001ea0,
        0x1ea100001ea2,
        0x1ea300001ea4,
        0x1ea500001ea6,
        0x1ea700001ea8,
        0x1ea900001eaa,
        0x1eab00001eac,
        0x1ead00001eae,
        0x1eaf00001eb0,
        0x1eb100001eb2,
        0x1eb300001eb4,
        0x1eb500001eb6,
        0x1eb700001eb8,
        0x1eb900001eba,
        0x1ebb00001ebc,
        0x1ebd00001ebe,
        0x1ebf00001ec0,
        0x1ec100001ec2,
        0x1ec300001ec4,
        0x1ec500001ec6,
        0x1ec700001ec8,
        0x1ec900001eca,
        0x1ecb00001ecc,
        0x1ecd00001ece,
        0x1ecf00001ed0,
        0x1ed100001ed2,
        0x1ed300001ed4,
        0x1ed500001ed6,
        0x1ed700001ed8,
        0x1ed900001eda,
        0x1edb00001edc,
        0x1edd00001ede,
        0x1edf00001ee0,
        0x1ee100001ee2,
        0x1ee300001ee4,
        0x1ee500001ee6,
        0x1ee700001ee8,
        0x1ee900001eea,
        0x1eeb00001eec,
        0x1eed00001eee,
        0x1eef00001ef0,
        0x1ef100001ef2,
        0x1ef300001ef4,
        0x1ef500001ef6,
        0x1ef700001ef8,
        0x1ef900001efa,
        0x1efb00001efc,
        0x1efd00001efe,
        0x1eff00001f08,
        0x1f1000001f16,
        0x1f2000001f28,
        0x1f3000001f38,
        0x1f4000001f46,
        0x1f5000001f58,
        0x1f6000001f68,
        0x1f7000001f71,
        0x1f7200001f73,
        0x1f7400001f75,
        0x1f7600001f77,
        0x1f7800001f79,
        0x1f7a00001f7b,
        0x1f7c00001f7d,
        0x1fb000001fb2,
        0x1fb600001fb7,
        0x1fc600001fc7,
        0x1fd000001fd3,
        0x1fd600001fd8,
        0x1fe000001fe3,
        0x1fe400001fe8,
        0x1ff600001ff7,
        0x214e0000214f,
        0x218400002185,
        0x2c3000002c60,
        0x2c6100002c62,
        0x2c6500002c67,
        0x2c6800002c69,
        0x2c6a00002c6b,
        0x2c6c00002c6d,
        0x2c7100002c72,
        0x2c7300002c75,
        0x2c7600002c7c,
        0x2c8100002c82,
        0x2c8300002c84,
        0x2c8500002c86,
        0x2c8700002c88,
        0x2c8900002c8a,
        0x2c8b00002c8c,
        0x2c8d00002c8e,
        0x2c8f00002c90,
        0x2c9100002c92,
        0x2c9300002c94,
        0x2c9500002c96,
        0x2c9700002c98,
        0x2c9900002c9a,
        0x2c9b00002c9c,
        0x2c9d00002c9e,
        0x2c9f00002ca0,
        0x2ca100002ca2,
        0x2ca300002ca4,
        0x2ca500002ca6,
        0x2ca700002ca8,
        0x2ca900002caa,
        0x2cab00002cac,
        0x2cad00002cae,
        0x2caf00002cb0,
        0x2cb100002cb2,
        0x2cb300002cb4,
        0x2cb500002cb6,
        0x2cb700002cb8,
        0x2cb900002cba,
        0x2cbb00002cbc,
        0x2cbd00002cbe,
        0x2cbf00002cc0,
        0x2cc100002cc2,
        0x2cc300002cc4,
        0x2cc500002cc6,
        0x2cc700002cc8,
        0x2cc900002cca,
        0x2ccb00002ccc,
        0x2ccd00002cce,
        0x2ccf00002cd0,
        0x2cd100002cd2,
        0x2cd300002cd4,
        0x2cd500002cd6,
        0x2cd700002cd8,
        0x2cd900002cda,
        0x2cdb00002cdc,
        0x2cdd00002cde,
        0x2cdf00002ce0,
        0x2ce100002ce2,
        0x2ce300002ce5,
        0x2cec00002ced,
        0x2cee00002cf2,
        0x2cf300002cf4,
        0x2d0000002d26,
        0x2d2700002d28,
        0x2d2d00002d2e,
        0x2d3000002d68,
        0x2d7f00002d97,
        0x2da000002da7,
        0x2da800002daf,
        0x2db000002db7,
        0x2db800002dbf,
        0x2dc000002dc7,
        0x2dc800002dcf,
        0x2dd000002dd7,
        0x2dd800002ddf,
        0x2de000002e00,
        0x2e2f00002e30,
        0x300500003008,
        0x302a0000302e,
        0x303c0000303d,
        0x304100003097,
        0x30990000309b,
        0x309d0000309f,
        0x30a1000030fb,
        0x30fc000030ff,
        0x310500003130,
        0x31a0000031c0,
        0x31f000003200,
        0x340000004dc0,
        0x4e000000a48d,
        0xa4d00000a4fe,
        0xa5000000a60d,
        0xa6100000a62c,
        0xa6410000a642,
        0xa6430000a644,
        0xa6450000a646,
        0xa6470000a648,
        0xa6490000a64a,
        0xa64b0000a64c,
        0xa64d0000a64e,
        0xa64f0000a650,
        0xa6510000a652,
        0xa6530000a654,
        0xa6550000a656,
        0xa6570000a658,
        0xa6590000a65a,
        0xa65b0000a65c,
        0xa65d0000a65e,
        0xa65f0000a660,
        0xa6610000a662,
        0xa6630000a664,
        0xa6650000a666,
        0xa6670000a668,
        0xa6690000a66a,
        0xa66b0000a66c,
        0xa66d0000a670,
        0xa6740000a67e,
        0xa67f0000a680,
        0xa6810000a682,
        0xa6830000a684,
        0xa6850000a686,
        0xa6870000a688,
        0xa6890000a68a,
        0xa68b0000a68c,
        0xa68d0000a68e,
        0xa68f0000a690,
        0xa6910000a692,
        0xa6930000a694,
        0xa6950000a696,
        0xa6970000a698,
        0xa6990000a69a,
        0xa69b0000a69c,
        0xa69e0000a6e6,
        0xa6f00000a6f2,
        0xa7170000a720,
        0xa7230000a724,
        0xa7250000a726,
        0xa7270000a728,
        0xa7290000a72a,
        0xa72b0000a72c,
        0xa72d0000a72e,
        0xa72f0000a732,
        0xa7330000a734,
        0xa7350000a736,
        0xa7370000a738,
        0xa7390000a73a,
        0xa73b0000a73c,
        0xa73d0000a73e,
        0xa73f0000a740,
        0xa7410000a742,
        0xa7430000a744,
        0xa7450000a746,
        0xa7470000a748,
        0xa7490000a74a,
        0xa74b0000a74c,
        0xa74d0000a74e,
        0xa74f0000a750,
        0xa7510000a752,
        0xa7530000a754,
        0xa7550000a756,
        0xa7570000a758,
        0xa7590000a75a,
        0xa75b0000a75c,
        0xa75d0000a75e,
        0xa75f0000a760,
        0xa7610000a762,
        0xa7630000a764,
        0xa7650000a766,
        0xa7670000a768,
        0xa7690000a76a,
        0xa76b0000a76c,
        0xa76d0000a76e,
        0xa76f0000a770,
        0xa7710000a779,
        0xa77a0000a77b,
        0xa77c0000a77d,
        0xa77f0000a780,
        0xa7810000a782,
        0xa7830000a784,
        0xa7850000a786,
        0xa7870000a789,
        0xa78c0000a78d,
        0xa78e0000a790,
        0xa7910000a792,
        0xa7930000a796,
        0xa7970000a798,
        0xa7990000a79a,
        0xa79b0000a79c,
        0xa79d0000a79e,
        0xa79f0000a7a0,
        0xa7a10000a7a2,
        0xa7a30000a7a4,
        0xa7a50000a7a6,
        0xa7a70000a7a8,
        0xa7a90000a7aa,
        0xa7af0000a7b0,
        0xa7b50000a7b6,
        0xa7b70000a7b8,
        0xa7b90000a7ba,
        0xa7bb0000a7bc,
        0xa7bd0000a7be,
        0xa7bf0000a7c0,
        0xa7c10000a7c2,
        0xa7c30000a7c4,
        0xa7c80000a7c9,
        0xa7ca0000a7cb,
        0xa7d10000a7d2,
        0xa7d30000a7d4,
        0xa7d50000a7d6,
        0xa7d70000a7d8,
        0xa7d90000a7da,
        0xa7f60000a7f8,
        0xa7fa0000a828,
        0xa82c0000a82d,
        0xa8400000a874,
        0xa8800000a8c6,
        0xa8d00000a8da,
        0xa8e00000a8f8,
        0xa8fb0000a8fc,
        0xa8fd0000a92e,
        0xa9300000a954,
        0xa9800000a9c1,
        0xa9cf0000a9da,
        0xa9e00000a9ff,
        0xaa000000aa37,
        0xaa400000aa4e,
        0xaa500000aa5a,
        0xaa600000aa77,
        0xaa7a0000aac3,
        0xaadb0000aade,
        0xaae00000aaf0,
        0xaaf20000aaf7,
        0xab010000ab07,
        0xab090000ab0f,
        0xab110000ab17,
        0xab200000ab27,
        0xab280000ab2f,
        0xab300000ab5b,
        0xab600000ab69,
        0xabc00000abeb,
        0xabec0000abee,
        0xabf00000abfa,
        0xac000000d7a4,
        0xfa0e0000fa10,
        0xfa110000fa12,
        0xfa130000fa15,
        0xfa1f0000fa20,
        0xfa210000fa22,
        0xfa230000fa25,
        0xfa270000fa2a,
        0xfb1e0000fb1f,
        0xfe200000fe30,
        0xfe730000fe74,
        0x100000001000c,
        0x1000d00010027,
        0x100280001003b,
        0x1003c0001003e,
        0x1003f0001004e,
        0x100500001005e,
        0x10080000100fb,
        0x101fd000101fe,
        0x102800001029d,
        0x102a0000102d1,
        0x102e0000102e1,
        0x1030000010320,
        0x1032d00010341,
        0x103420001034a,
        0x103500001037b,
        0x103800001039e,
        0x103a0000103c4,
        0x103c8000103d0,
        0x104280001049e,
        0x104a0000104aa,
        0x104d8000104fc,
        0x1050000010528,
        0x1053000010564,
        0x10597000105a2,
        0x105a3000105b2,
        0x105b3000105ba,
        0x105bb000105bd,
        0x1060000010737,
        0x1074000010756,
        0x1076000010768,
        0x1078000010781,
        0x1080000010806,
        0x1080800010809,
        0x1080a00010836,
        0x1083700010839,
        0x1083c0001083d,
        0x1083f00010856,
        0x1086000010877,
        0x108800001089f,
        0x108e0000108f3,
        0x108f4000108f6,
        0x1090000010916,
        0x109200001093a,
        0x10980000109b8,
        0x109be000109c0,
        0x10a0000010a04,
        0x10a0500010a07,
        0x10a0c00010a14,
        0x10a1500010a18,
        0x10a1900010a36,
        0x10a3800010a3b,
        0x10a3f00010a40,
        0x10a6000010a7d,
        0x10a8000010a9d,
        0x10ac000010ac8,
        0x10ac900010ae7,
        0x10b0000010b36,
        0x10b4000010b56,
        0x10b6000010b73,
        0x10b8000010b92,
        0x10c0000010c49,
        0x10cc000010cf3,
        0x10d0000010d28,
        0x10d3000010d3a,
        0x10e8000010eaa,
        0x10eab00010ead,
        0x10eb000010eb2,
        0x10efd00010f1d,
        0x10f2700010f28,
        0x10f3000010f51,
        0x10f7000010f86,
        0x10fb000010fc5,
        0x10fe000010ff7,
        0x1100000011047,
        0x1106600011076,
        0x1107f000110bb,
        0x110c2000110c3,
        0x110d0000110e9,
        0x110f0000110fa,
        0x1110000011135,
        0x1113600011140,
        0x1114400011148,
        0x1115000011174,
        0x1117600011177,
        0x11180000111c5,
        0x111c9000111cd,
        0x111ce000111db,
        0x111dc000111dd,
        0x1120000011212,
        0x1121300011238,
        0x1123e00011242,
        0x1128000011287,
        0x1128800011289,
        0x1128a0001128e,
        0x1128f0001129e,
        0x1129f000112a9,
        0x112b0000112eb,
        0x112f0000112fa,
        0x1130000011304,
        0x113050001130d,
        0x1130f00011311,
        0x1131300011329,
        0x1132a00011331,
        0x1133200011334,
        0x113350001133a,
        0x1133b00011345,
        0x1134700011349,
        0x1134b0001134e,
        0x1135000011351,
        0x1135700011358,
        0x1135d00011364,
        0x113660001136d,
        0x1137000011375,
        0x114000001144b,
        0x114500001145a,
        0x1145e00011462,
        0x11480000114c6,
        0x114c7000114c8,
        0x114d0000114da,
        0x11580000115b6,
        0x115b8000115c1,
        0x115d8000115de,
        0x1160000011641,
        0x1164400011645,
        0x116500001165a,
        0x11680000116b9,
        0x116c0000116ca,
        0x117000001171b,
        0x1171d0001172c,
        0x117300001173a,
        0x1174000011747,
        0x118000001183b,
        0x118c0000118ea,
        0x118ff00011907,
        0x119090001190a,
        0x1190c00011914,
        0x1191500011917,
        0x1191800011936,
        0x1193700011939,
        0x1193b00011944,
        0x119500001195a,
        0x119a0000119a8,
        0x119aa000119d8,
        0x119da000119e2,
        0x119e3000119e5,
        0x11a0000011a3f,
        0x11a4700011a48,
        0x11a5000011a9a,
        0x11a9d00011a9e,
        0x11ab000011af9,
        0x11c0000011c09,
        0x11c0a00011c37,
        0x11c3800011c41,
        0x11c5000011c5a,
        0x11c7200011c90,
        0x11c9200011ca8,
        0x11ca900011cb7,
        0x11d0000011d07,
        0x11d0800011d0a,
        0x11d0b00011d37,
        0x11d3a00011d3b,
        0x11d3c00011d3e,
        0x11d3f00011d48,
        0x11d5000011d5a,
        0x11d6000011d66,
        0x11d6700011d69,
        0x11d6a00011d8f,
        0x11d9000011d92,
        0x11d9300011d99,
        0x11da000011daa,
        0x11ee000011ef7,
        0x11f0000011f11,
        0x11f1200011f3b,
        0x11f3e00011f43,
        0x11f5000011f5a,
        0x11fb000011fb1,
        0x120000001239a,
        0x1248000012544,
        0x12f9000012ff1,
        0x1300000013430,
        0x1344000013456,
        0x1440000014647,
        0x1680000016a39,
        0x16a4000016a5f,
        0x16a6000016a6a,
        0x16a7000016abf,
        0x16ac000016aca,
        0x16ad000016aee,
        0x16af000016af5,
        0x16b0000016b37,
        0x16b4000016b44,
        0x16b5000016b5a,
        0x16b6300016b78,
        0x16b7d00016b90,
        0x16e6000016e80,
        0x16f0000016f4b,
        0x16f4f00016f88,
        0x16f8f00016fa0,
        0x16fe000016fe2,
        0x16fe300016fe5,
        0x16ff000016ff2,
        0x17000000187f8,
        0x1880000018cd6,
        0x18d0000018d09,
        0x1aff00001aff4,
        0x1aff50001affc,
        0x1affd0001afff,
        0x1b0000001b123,
        0x1b1320001b133,
        0x1b1500001b153,
        0x1b1550001b156,
        0x1b1640001b168,
        0x1b1700001b2fc,
        0x1bc000001bc6b,
        0x1bc700001bc7d,
        0x1bc800001bc89,
        0x1bc900001bc9a,
        0x1bc9d0001bc9f,
        0x1cf000001cf2e,
        0x1cf300001cf47,
        0x1da000001da37,
        0x1da3b0001da6d,
        0x1da750001da76,
        0x1da840001da85,
        0x1da9b0001daa0,
        0x1daa10001dab0,
        0x1df000001df1f,
        0x1df250001df2b,
        0x1e0000001e007,
        0x1e0080001e019,
        0x1e01b0001e022,
        0x1e0230001e025,
        0x1e0260001e02b,
        0x1e08f0001e090,
        0x1e1000001e12d,
        0x1e1300001e13e,
        0x1e1400001e14a,
        0x1e14e0001e14f,
        0x1e2900001e2af,
        0x1e2c00001e2fa,
        0x1e4d00001e4fa,
        0x1e7e00001e7e7,
        0x1e7e80001e7ec,
        0x1e7ed0001e7ef,
        0x1e7f00001e7ff,
        0x1e8000001e8c5,
        0x1e8d00001e8d7,
        0x1e9220001e94c,
        0x1e9500001e95a,
        0x200000002a6e0,
        0x2a7000002b73a,
        0x2b7400002b81e,
        0x2b8200002cea2,
        0x2ceb00002ebe1,
        0x2ebf00002ee5e,
        0x300000003134b,
        0x31350000323b0,
    ),
    'CONTEXTJ': (
        0x200c0000200e,
    ),
    'CONTEXTO': (
        0xb7000000b8,
        0x37500000376,
        0x5f3000005f5,
        0x6600000066a,
        0x6f0000006fa,
        0x30fb000030fc,
    ),
}
