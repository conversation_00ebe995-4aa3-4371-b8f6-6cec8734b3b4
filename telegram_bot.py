import asyncio
import aiohttp
import math
import json
import io
import pandas as pd
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes, CallbackQueryHandler

# Телеграм бот токени
BOT_TOKEN = "7960086091:AAFuUhT2Tp-t1pinK7J1TrWBigegIk-cGQI"

# Mapbox API калити
MAPBOX_API_KEY = "pk.eyJ1Ijoic2FtY2hpazAiLCJhIjoiY21iOTN1NHNhMDlxbzJrcG96a21iOG91OSJ9.qY"

class ReliefCalculator:
    def __init__(self):
        self.session = None

    async def init_session(self):
        if not self.session:
            self.session = aiohttp.ClientSession()

    async def close_session(self):
        if self.session:
            await self.session.close()

    def parse_coordinate_data(self, text):
        """Координата маълумотларини парсинг қилиш"""
        lines = text.strip().split('\n')
        data = []

        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue

            try:
                # Янги формат: "41.2988891601562 69.1905517578125;180;890"
                if ';' in line:
                    parts = line.split(';')
                    if len(parts) >= 3:
                        # Координаталарни бўлиш
                        coord_part = parts[0].strip()
                        if ' ' in coord_part:
                            # Бўш жой билан ажратилган
                            coord_parts = coord_part.split()
                            if len(coord_parts) >= 2:
                                lat = float(coord_parts[0])
                                lon = float(coord_parts[1])
                                azimuth = float(parts[1])
                                distance = float(parts[2])
                        else:
                            # Эски формат
                            if len(parts) >= 4:
                                lat = float(parts[0])
                                lon = float(parts[1])
                                azimuth = float(parts[2])
                                distance = float(parts[3])
                            else:
                                continue

                        # Валидация
                        if self.validate_coordinates(lat, lon, azimuth, distance):
                            data.append({
                                'row': i,
                                'lat1': lat,
                                'lon1': lon,
                                'azimuth': azimuth,
                                'distance': distance,
                                'original': line
                            })
                        else:
                            print(f"Қатор {i}: Нотўғри қийматлар")

            except (ValueError, IndexError) as e:
                print(f"Қатор {i} да хато: {e}")
                continue

        return data

    def validate_coordinates(self, lat, lon, azimuth, distance):
        """Координаталарни валидация қилиш"""
        if lat < -90 or lat > 90:
            return False
        if lon < -180 or lon > 180:
            return False
        if azimuth < 0 or azimuth >= 360:
            return False
        if distance <= 0 or distance > 100000:
            return False
        return True

    def calculate_second_coordinate(self, lat1, lon1, azimuth, distance):
        """Иккинчи координатани хисоблаш"""
        R = 6371000  # Ер радиуси метрда
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        azimuth_rad = math.radians(azimuth)

        lat2_rad = math.asin(
            math.sin(lat1_rad) * math.cos(distance / R) +
            math.cos(lat1_rad) * math.sin(distance / R) * math.cos(azimuth_rad)
        )

        lon2_rad = lon1_rad + math.atan2(
            math.sin(azimuth_rad) * math.sin(distance / R) * math.cos(lat1_rad),
            math.cos(distance / R) - math.sin(lat1_rad) * math.sin(lat2_rad)
        )

        return {
            'lat': math.degrees(lat2_rad),
            'lon': math.degrees(lon2_rad)
        }

    async def get_elevation(self, lat, lon):
        """Mapbox API орқали баландликни олиш"""
        await self.init_session()

        url = f"https://api.mapbox.com/v4/mapbox.mapbox-terrain-v2/tilequery/{lon},{lat}.json"
        params = {
            'layers': 'contour',
            'limit': 50,
            'access_token': MAPBOX_API_KEY
        }

        try:
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()

                    if data.get('features'):
                        # Энг яқин контур чизиғини топиш
                        closest_elevation = 0
                        min_distance = float('inf')

                        for feature in data['features']:
                            if feature.get('properties', {}).get('ele') is not None:
                                feature_coords = feature['geometry']['coordinates']

                                if feature['geometry']['type'] == 'Point':
                                    feature_lon, feature_lat = feature_coords
                                elif feature['geometry']['type'] == 'LineString':
                                    # Чизиқнинг ўртасини олиш
                                    mid_index = len(feature_coords) // 2
                                    feature_lon, feature_lat = feature_coords[mid_index]
                                else:
                                    continue

                                distance = self.calculate_distance(lat, lon, feature_lat, feature_lon)

                                if distance < min_distance:
                                    min_distance = distance
                                    closest_elevation = feature['properties']['ele']

                        return closest_elevation

                    return 0
                else:
                    print(f"API хатоси: {response.status}")
                    return 0

        except Exception as e:
            print(f"Баландлик олишда хато: {e}")
            return 0

    def calculate_distance(self, lat1, lon1, lat2, lon2):
        """Иккита нуқта орасидаги масофани хисоблаш"""
        R = 6371000
        dlat = math.radians(lat2 - lat1)
        dlon = math.radians(lon2 - lon1)
        a = (math.sin(dlat/2) * math.sin(dlat/2) +
             math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) *
             math.sin(dlon/2) * math.sin(dlon/2))
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        return R * c

    async def process_data(self, data, progress_callback=None):
        """Маълумотларни ишлаш"""
        results = []
        total = len(data)

        for i, item in enumerate(data):
            try:
                # Иккинчи координатани хисоблаш
                coord2 = self.calculate_second_coordinate(
                    item['lat1'], item['lon1'], item['azimuth'], item['distance']
                )

                # Баландликларни олиш
                elevation1 = await self.get_elevation(item['lat1'], item['lon1'])
                await asyncio.sleep(0.2)  # API лимитини олдини олиш

                elevation2 = await self.get_elevation(coord2['lat'], coord2['lon'])
                await asyncio.sleep(0.2)

                relief_difference = abs(elevation2 - elevation1)

                result = {
                    **item,
                    'lat2': coord2['lat'],
                    'lon2': coord2['lon'],
                    'elevation1': elevation1,
                    'elevation2': elevation2,
                    'relief_difference': relief_difference,
                    'status': 'success'
                }

                results.append(result)

                # Прогресс хабар бериш
                if progress_callback:
                    progress = int((i + 1) / total * 100)
                    await progress_callback(progress, i + 1, total)

            except Exception as e:
                print(f"Қатор {item['row']} да хато: {e}")
                results.append({
                    **item,
                    'relief_difference': 0,
                    'status': 'error',
                    'error': str(e)
                })

        return results

# Глобал калькулятор объекти
calculator = ReliefCalculator()

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Бот бошланиши"""
    welcome_text = """
🏔️ **Рельеф Фарки Хисоблагич Ботига хуш келибсиз!**

Бу бот координаталар, азимут ва масофа асосида рельеф фаркини хисоблайди.

📋 **Ишлатиш:**
1. Координаталарингизни қуйидаги форматда юборинг:
   `41.2988891601562 69.1905517578125;180;890`

2. Ёки бир нечта қаторни:
   ```
   41.2988891601562 69.1905517578125;180;890
   41.2849998474121 69.1852798461914;20;670
   ```

🔧 **Формат тушунтириши:**
- `41.2988891601562 69.1905517578125` - кенглик ва узунлик
- `180` - азимут (градус)
- `890` - масофа (метр)

📊 Натижада сиз олишингиз:
- Иккинчи координата
- Ҳар иккала нуқтанинг баландлиги
- Рельеф фарки
- Excel файл

/help - ёрдам
/example - мисол
"""
    await update.message.reply_text(welcome_text, parse_mode='Markdown')

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Ёрдам команда"""
    help_text = """
🆘 **Ёрдам**

**Қўллаб-қувватланадиган форматлар:**
1. `41.2988891601562 69.1905517578125;180;890`
2. `41.2988891601562;69.1905517578125;180;890`

**Чекловлар:**
- Кенглик: -90° дан +90° гача
- Узунлик: -180° дан +180° гача
- Азимут: 0° дан 360° гача
- Масофа: 100км гача

**Мисол:**
/example командасини ишлатинг
"""
    await update.message.reply_text(help_text, parse_mode='Markdown')

async def example_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Мисол команда"""
    example_text = """
📝 **Мисол маълумотлар:**

```
41.2988891601562 69.1905517578125;180;890
41.2849998474121 69.1852798461914;20;670
41.3000000000000 69.2000000000000;90;1000
```

Бу маълумотларни копия қилиб, ботга юборинг!
"""
    await update.message.reply_text(example_text, parse_mode='Markdown')

async def process_coordinates(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Координаталарни ишлаш"""
    text = update.message.text

    # Маълумотларни парсинг қилиш
    data = calculator.parse_coordinate_data(text)

    if not data:
        await update.message.reply_text(
            "❌ Маълумотлар топилмади ёки нотўғри формат!\n\n"
            "Тўғри формат: `41.2988891601562 69.1905517578125;180;890`\n\n"
            "/example - мисол учун",
            parse_mode='Markdown'
        )
        return

    # Тасдиқлаш хабари
    confirmation_text = f"""
✅ **{len(data)} та қатор топилди**

📋 **Маълумотлар:**
"""

    for item in data[:5]:  # Биринчи 5 тасини кўрсатиш
        confirmation_text += f"• {item['original']}\n"

    if len(data) > 5:
        confirmation_text += f"... ва яна {len(data) - 5} та қатор\n"

    confirmation_text += "\n🚀 Хисоблашни бошлайми?"

    keyboard = [
        [InlineKeyboardButton("✅ Ҳа, бошлаш", callback_data=f"process_{len(data)}")],
        [InlineKeyboardButton("❌ Бекор қилиш", callback_data="cancel")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # Маълумотларни сақлаш
    context.user_data['coordinate_data'] = data

    await update.message.reply_text(
        confirmation_text,
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )

async def button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Тугма босилганда"""
    query = update.callback_query
    await query.answer()

    if query.data.startswith("process_"):
        # Хисоблашни бошлаш
        data = context.user_data.get('coordinate_data', [])

        if not data:
            await query.edit_message_text("❌ Маълумотлар топилмади!")
            return

        # Прогресс хабари
        progress_message = await query.edit_message_text(
            "🔄 **Хисоблаш бошланди...**\n\n"
            f"📊 Жами: {len(data)} та қатор\n"
            "⏳ Прогресс: 0%",
            parse_mode='Markdown'
        )

        # Прогресс янгилаш функцияси
        async def update_progress(progress, current, total):
            try:
                await progress_message.edit_text(
                    "🔄 **Хисоблаш давом етмоқда...**\n\n"
                    f"📊 Жами: {total} та қатор\n"
                    f"✅ Тайёр: {current}/{total}\n"
                    f"⏳ Прогресс: {progress}%",
                    parse_mode='Markdown'
                )
            except:
                pass  # Telegram лимити учун

        # Маълумотларни ишлаш
        results = await calculator.process_data(data, update_progress)

        # Натижаларни сақлаш
        context.user_data['results'] = results

        # Натижаларни тайёрлаш
        await show_results(query, results)

    elif query.data == "cancel":
        await query.edit_message_text("❌ Бекор қилинди")

    elif query.data == "download_excel":
        # Excel файлни юклаб олиш
        results = context.user_data.get('results', [])
        if results:
            await send_excel_file(query, results)
        else:
            await query.answer("❌ Натижалар топилмади!")

    elif query.data == "show_map":
        # Картада кўрсатиш
        results = context.user_data.get('results', [])
        if results:
            await send_map_info(query, results)
        else:
            await query.answer("❌ Натижалар топилмади!")

    elif query.data == "back_to_results":
        # Натижаларга қайтиш
        results = context.user_data.get('results', [])
        if results:
            await show_results(query, results)
        else:
            await query.answer("❌ Натижалар топилмади!")

async def show_results(query, results):
    """Натижаларни кўрсатиш"""
    success_count = len([r for r in results if r['status'] == 'success'])
    error_count = len([r for r in results if r['status'] == 'error'])

    success_results = [r for r in results if r['status'] == 'success']
    avg_relief = sum(r['relief_difference'] for r in success_results) / len(success_results) if success_results else 0
    max_relief = max(r['relief_difference'] for r in success_results) if success_results else 0
    min_relief = min(r['relief_difference'] for r in success_results) if success_results else 0

    result_text = f"""
🎉 **Хисоблаш тугади!**

📊 **Статистика:**
• Жами қаторлар: {len(results)}
• Муваффақиятли: {success_count}
• Хатолар: {error_count}
• Ўртача рельеф фарки: {avg_relief:.1f}м
• Максимал фарк: {max_relief:.1f}м
• Минимал фарк: {min_relief:.1f}м

📋 **Биринчи натижалар:**
"""

    # Биринчи 3 та натижани кўрсатиш
    for i, result in enumerate(results[:3]):
        if result['status'] == 'success':
            result_text += f"""
**{i+1}-қатор:**
• Биринчи: {result['lat1']:.6f}, {result['lon1']:.6f}
• Иккинчи: {result['lat2']:.6f}, {result['lon2']:.6f}
• Рельеф фарки: {result['relief_difference']:.1f}м
"""
        else:
            result_text += f"**{i+1}-қатор:** ❌ Хато\n"

    if len(results) > 3:
        result_text += f"\n... ва яна {len(results) - 3} та қатор"

    keyboard = [
        [InlineKeyboardButton("📥 Excel юклаб олиш", callback_data="download_excel")],
        [InlineKeyboardButton("🗺️ Картада кўриш", callback_data="show_map")]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    await query.edit_message_text(
        result_text,
        reply_markup=reply_markup,
        parse_mode='Markdown'
    )

async def send_excel_file(query, results):
    """Excel файлни юбориш"""
    try:
        # Excel маълумотларини тайёрлаш
        excel_data = []
        headers = [
            'Биринчи координата',
            'Рельеф фарки (м)',
            'Иккинчи координата',
            'Биринчи баландлик (м)',
            'Иккинчи баландлик (м)',
            'Статус'
        ]
        excel_data.append(headers)

        for result in results:
            if result['status'] == 'success':
                coord2_text = f"{result['lat2']:.6f};{result['lon2']:.6f}"
                relief_text = f"{result['relief_difference']:.1f}"
                elevation1_text = f"{result['elevation1']:.1f}"
                elevation2_text = f"{result['elevation2']:.1f}"
                status_text = "Тайёр"
            else:
                coord2_text = ""
                relief_text = ""
                elevation1_text = ""
                elevation2_text = ""
                status_text = "Хато"

            excel_data.append([
                result['original'],
                relief_text,
                coord2_text,
                elevation1_text,
                elevation2_text,
                status_text
            ])

        # DataFrame яратиш
        df = pd.DataFrame(excel_data[1:], columns=excel_data[0])

        # Excel файлга сақлаш
        excel_buffer = io.BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Натижалар', index=False)

        excel_buffer.seek(0)

        # Файлни юбориш
        from datetime import datetime
        filename = f"relief_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        await query.message.reply_document(
            document=excel_buffer,
            filename=filename,
            caption="📊 **Рельеф фарки натижалари**\n\nExcel файлингиз тайёр!",
            parse_mode='Markdown'
        )

        await query.answer("✅ Excel файл юборилди!")

    except Exception as e:
        print(f"Excel файл яратишда хато: {e}")
        await query.answer("❌ Excel файл яратишда хато!")

async def send_map_info(query, results):
    """Карта маълумотларини юбориш"""
    try:
        success_results = [r for r in results if r['status'] == 'success']

        if not success_results:
            await query.answer("❌ Муваффақиятли натижалар топилмади!")
            return

        map_text = "🗺️ **Карта маълумотлари**\n\n"

        # Ҳар бир нуқта учун Google Maps ҳаволаси
        for i, result in enumerate(success_results[:5], 1):
            lat1, lon1 = result['lat1'], result['lon1']
            lat2, lon2 = result['lat2'], result['lon2']

            # Google Maps ҳаволалари
            map1_url = f"https://maps.google.com/?q={lat1},{lon1}"
            map2_url = f"https://maps.google.com/?q={lat2},{lon2}"

            map_text += f"""
**{i}-нуқта жуфти:**
📍 Биринчи: [{lat1:.6f}, {lon1:.6f}]({map1_url})
📍 Иккинчи: [{lat2:.6f}, {lon2:.6f}]({map2_url})
📏 Рельеф фарки: {result['relief_difference']:.1f}м
"""

        if len(success_results) > 5:
            map_text += f"\n... ва яна {len(success_results) - 5} та нуқта жуфти"

        # Умумий карта ҳаволаси (биринчи нуқта марказида)
        center_lat = success_results[0]['lat1']
        center_lon = success_results[0]['lon1']
        general_map_url = f"https://maps.google.com/?q={center_lat},{center_lon}&z=12"

        map_text += f"\n\n🌍 [Умумий картада кўриш]({general_map_url})"

        keyboard = [
            [InlineKeyboardButton("🔙 Орқага", callback_data="back_to_results")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(
            map_text,
            reply_markup=reply_markup,
            parse_mode='Markdown',
            disable_web_page_preview=True
        )

    except Exception as e:
        print(f"Карта маълумотларини юборишда хато: {e}")
        await query.answer("❌ Карта маълумотларини юборишда хато!")

def main():
    """Асосий функция"""
    application = Application.builder().token(BOT_TOKEN).build()

    # Командалар
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("example", example_command))

    # Хабарлар
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, process_coordinates))

    # Тугмалар
    application.add_handler(CallbackQueryHandler(button_callback))

    print("🤖 Бот ишга тушди...")
    application.run_polling()

if __name__ == '__main__':
    main()
