#!/usr/bin/env python3
"""
Оддий тест - кутубхоналарсиз
"""

import math

def parse_coordinate_data(text):
    """Координата маълумотларини парсинг қилиш"""
    lines = text.strip().split('\n')
    data = []
    
    for i, line in enumerate(lines, 1):
        line = line.strip()
        if not line:
            continue
            
        try:
            # Янги формат: "41.2988891601562 69.1905517578125;180;890"
            if ';' in line:
                parts = line.split(';')
                if len(parts) >= 3:
                    # Координаталарни бўлиш
                    coord_part = parts[0].strip()
                    if ' ' in coord_part:
                        # Бўш жой билан ажратилган
                        coord_parts = coord_part.split()
                        if len(coord_parts) >= 2:
                            lat = float(coord_parts[0])
                            lon = float(coord_parts[1])
                            azimuth = float(parts[1])
                            distance = float(parts[2])
                            
                            data.append({
                                'row': i,
                                'lat1': lat,
                                'lon1': lon,
                                'azimuth': azimuth,
                                'distance': distance,
                                'original': line
                            })
                            
        except (ValueError, IndexError) as e:
            print(f"Қатор {i} да хато: {e}")
            continue
    
    return data

def calculate_second_coordinate(lat1, lon1, azimuth, distance):
    """Иккинчи координатани хисоблаш"""
    R = 6371000  # Ер радиуси метрда
    lat1_rad = math.radians(lat1)
    lon1_rad = math.radians(lon1)
    azimuth_rad = math.radians(azimuth)
    
    lat2_rad = math.asin(
        math.sin(lat1_rad) * math.cos(distance / R) +
        math.cos(lat1_rad) * math.sin(distance / R) * math.cos(azimuth_rad)
    )
    
    lon2_rad = lon1_rad + math.atan2(
        math.sin(azimuth_rad) * math.sin(distance / R) * math.cos(lat1_rad),
        math.cos(distance / R) - math.sin(lat1_rad) * math.sin(lat2_rad)
    )
    
    return {
        'lat': math.degrees(lat2_rad),
        'lon': math.degrees(lon2_rad)
    }

def main():
    print("🧪 Оддий тест")
    print("=" * 40)
    
    # Тест маълумотлари
    test_data = """41.2988891601562 69.1905517578125;180;890
41.2849998474121 69.1852798461914;20;670"""
    
    print(f"📝 Тест маълумотлари:\n{test_data}\n")
    
    # Маълумотларни парсинг қилиш
    data = parse_coordinate_data(test_data)
    print(f"✅ {len(data)} та қатор парсинг қилинди\n")
    
    for item in data:
        print(f"📍 Қатор {item['row']}:")
        print(f"   Биринчи: {item['lat1']:.6f}, {item['lon1']:.6f}")
        print(f"   Азимут: {item['azimuth']}°")
        print(f"   Масофа: {item['distance']}м")
        
        # Иккинчи координатани хисоблаш
        coord2 = calculate_second_coordinate(
            item['lat1'], item['lon1'], item['azimuth'], item['distance']
        )
        print(f"   Иккинчи: {coord2['lat']:.6f}, {coord2['lon']:.6f}")
        print()
    
    print("✅ Тест муваффақиятли тугади!")
    print("\n📋 Кейинги қадамлар:")
    print("1. Кутубхоналарни ўрнатинг: pip install -r requirements.txt")
    print("2. Телеграм ботни ишга тушириш: python telegram_bot.py")

if __name__ == "__main__":
    main()
