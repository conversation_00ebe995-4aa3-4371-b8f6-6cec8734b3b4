# Рельеф Фарки Хисоблагич

Бу дастур Excel файлдаги координаталар, азимут ва масофа маълумотлари асосида иккинчи координатани хисоблаб, Mapbox API орқали рельеф фаркини аниқлайди.

## Хусусиятлар

- Excel файлларни ўқиш (.xlsx, .xls)
- Азимут ва масофа орқали иккинчи координатани хисоблаш
- Mapbox API орқали баландлик маълумотларини олиш
- Рельеф фаркини хисоблаш
- Натижаларни Excel форматида юклаб олиш
- Прогресс кўрсаткичи
- Хатоларни бошқариш

## Ишлатилиши

### 1. Дастурни ишга тушириш

```bash
python -m http.server 8000
```

Сўнгра браузерда `http://localhost:8000` га киринг.

### 2. Excel файл форматини тайёрлаш

Excel файлнинг биринчи устунида қуйидаги форматда маълумот бўлиши керак:

```
41.2988891601562 69.1905517578125;180;890
```

Бу ерда:

- `41.2988891601562 69.1905517578125` - кенглик ва узунлик (бўш жой билан ажратилган)
- `180` - азимут (градусда)
- `890` - масофа (метрда)

**Эски формат ҳам қўллаб-қувватланади:**

```
41.2988891601562;69.1905517578125;180;890
```

### 3. Mapbox API калити

Дастурда Mapbox API калити керак. Сиз ўзингизнинг калитингизни https://console.mapbox.com/ дан олишингиз мумкин.

### 4. Дастурни ишлатиш қадамлари

1. **Excel файлни юклаш**: "Excel файлни танланг" тугмасини босиб файлингизни юкланг
2. **API калитини киритиш**: Mapbox API калитингизни киритинг
3. **Хисоблашни бошлаш**: "Хисоблашни бошлаш" тугмасини босинг
4. **Натижаларни юклаб олиш**: Хисоблаш тугагач "Натижани юклаб олиш" тугмасини босинг

## Натижа файли

Натижа файлида қуйидаги устунлар бўлади:

- **Биринчи координата**: Дастлабки маълумот
- **Рельеф фарки (м)**: Иккала нуқта орасидаги баландлик фарки
- **Иккинчи координата**: Хисобланган иккинчи нуқта координаталари
- **Биринчи баландлик (м)**: Биринчи нуқтанинг баландлиги
- **Иккинчи баландлик (м)**: Иккинчи нуқтанинг баландлиги
- **Статус**: Хисоблаш натижаси (Тайёр/Хато)

## Техник маълумотлар

### Координата хисоблаш формуласи

Дастур сферик геометрия формулаларидан фойдаланиб, биринчи координата, азимут ва масофа асосида иккинчи координатани хисоблайди:

```javascript
lat2 = asin(sin(lat1) * cos(d / R) + cos(lat1) * sin(d / R) * cos(azimuth));
lon2 =
  lon1 +
  atan2(
    sin(azimuth) * sin(d / R) * cos(lat1),
    cos(d / R) - sin(lat1) * sin(lat2)
  );
```

Бу ерда:

- `R = 6371000` м (Ер радиуси)
- `d` - масофа метрда
- `azimuth` - азимут радианда

### Mapbox API

Дастур Mapbox Tilequery API дан фойдаланиб баландлик маълумотларини олади:

```
https://api.mapbox.com/v4/mapbox.mapbox-terrain-v2/tilequery/{lon},{lat}.json?layers=contour&limit=50&access_token={API_KEY}
```

## Тест файли

Лойихада `test_data.csv` файли мавжуд бўлиб, уни Excel форматига ўгириб синаб кўришингиз мумкин.

## Хатоларни ҳал қилиш

### CORS хатоси

Агар CORS хатоси юзага келса, дастурни HTTP сервер орқали ишлатинг (localhost орқали).

### API лимити

Mapbox API лимитини олдини олиш учун дастур ҳар бир сўров орасида 200мс кутади.

### Баландлик маълумоти топилмаса

Агар Mapbox API дан баландлик маълумоти олинмаса, 0 қиймати қайтарилади.

## Муаллиф

Бу дастур Узбекистон координаталари билан ишлаш учун мосланган.
