class ReliefCalculator {
    constructor() {
        this.data = [];
        this.results = [];
        this.apiKey = '';
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        const fileInput = document.getElementById('fileInput');
        const processBtn = document.getElementById('processBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const apiKeyInput = document.getElementById('apiKey');

        fileInput.addEventListener('change', (e) => this.handleFileUpload(e));
        processBtn.addEventListener('click', () => this.processData());
        downloadBtn.addEventListener('click', () => this.downloadResults());
        apiKeyInput.addEventListener('input', (e) => this.updateApiKey(e.target.value));

        // API калитини дастлабки қийматдан олиш
        this.updateApiKey(apiKeyInput.value);
    }

    updateApiKey(key) {
        this.apiKey = key.trim();
        this.checkReadyState();

        // API калитини текшириш
        if (this.apiKey && this.apiKey.startsWith('pk.')) {
            this.testApiKey();
        }
    }

    async testApiKey() {
        try {
            // Тошкент координаталари билан тест
            const testLat = 41.2995;
            const testLon = 69.2401;
            const url = `https://api.mapbox.com/v4/mapbox.mapbox-terrain-v2/tilequery/${testLon},${testLat}.json?layers=contour&limit=1&access_token=${this.apiKey}`;

            const response = await fetch(url);
            const apiKeyInput = document.getElementById('apiKey');

            if (response.ok) {
                apiKeyInput.style.borderColor = '#4CAF50';
                apiKeyInput.title = 'API калити тўғри';
            } else {
                apiKeyInput.style.borderColor = '#f44336';
                apiKeyInput.title = 'API калити нотўғри ёки лимит тугаган';
            }
        } catch (error) {
            console.warn('API калитини текширишда хато:', error);
        }
    }

    checkReadyState() {
        const processBtn = document.getElementById('processBtn');
        processBtn.disabled = !(this.data.length > 0 && this.apiKey);
    }

    handleFileUpload(event) {
        const file = event.target.files[0];
        if (!file) return;

        const fileInfo = document.getElementById('fileInfo');
        fileInfo.innerHTML = `<strong>Файл:</strong> ${file.name} (${(file.size / 1024).toFixed(2)} KB)`;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const workbook = XLSX.read(e.target.result, { type: 'binary' });
                const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
                const jsonData = XLSX.utils.sheet_to_json(firstSheet, { header: 1 });

                this.parseExcelData(jsonData);
                this.showPreview();
                this.checkReadyState();
            } catch (error) {
                this.showError('Excel файлни ўқишда хато: ' + error.message);
            }
        };
        reader.readAsBinaryString(file);
    }

    parseExcelData(jsonData) {
        this.data = [];

        for (let i = 0; i < jsonData.length; i++) {
            const row = jsonData[i];
            if (!row[0]) continue; // Бўш қаторларни ўтказиб юбориш

            try {
                const parts = row[0].toString().split(';');
                if (parts.length >= 4) {
                    const lat = parseFloat(parts[0]);
                    const lon = parseFloat(parts[1]);
                    const azimuth = parseFloat(parts[2]);
                    const distance = parseFloat(parts[3]);

                    if (!isNaN(lat) && !isNaN(lon) && !isNaN(azimuth) && !isNaN(distance)) {
                        this.data.push({
                            rowIndex: i + 1,
                            lat1: lat,
                            lon1: lon,
                            azimuth: azimuth,
                            distance: distance,
                            originalData: row[0]
                        });
                    }
                }
            } catch (error) {
                console.warn(`Қатор ${i + 1} да хато:`, error);
            }
        }

        console.log(`${this.data.length} та тўғри қатор топилди`);
    }

    showPreview() {
        const previewSection = document.getElementById('previewSection');
        const previewBody = document.getElementById('previewBody');

        previewBody.innerHTML = '';

        const previewData = this.data.slice(0, 5); // Биринчи 5 та қаторни кўрсатиш

        previewData.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.originalData}</td>
                <td>-</td>
                <td>-</td>
                <td>Кутилмоқда</td>
            `;
            previewBody.appendChild(row);
        });

        if (this.data.length > 5) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="4" style="text-align: center; font-style: italic;">
                    ... ва яна ${this.data.length - 5} та қатор
                </td>
            `;
            previewBody.appendChild(row);
        }

        previewSection.style.display = 'block';
    }

    // Азимут ва масофа орқали иккинчи координатани хисоблаш
    calculateSecondCoordinate(lat1, lon1, azimuth, distance) {
        const R = 6371000; // Ер радиуси метрда
        const lat1Rad = lat1 * Math.PI / 180;
        const lon1Rad = lon1 * Math.PI / 180;
        const azimuthRad = azimuth * Math.PI / 180;

        const lat2Rad = Math.asin(
            Math.sin(lat1Rad) * Math.cos(distance / R) +
            Math.cos(lat1Rad) * Math.sin(distance / R) * Math.cos(azimuthRad)
        );

        const lon2Rad = lon1Rad + Math.atan2(
            Math.sin(azimuthRad) * Math.sin(distance / R) * Math.cos(lat1Rad),
            Math.cos(distance / R) - Math.sin(lat1Rad) * Math.sin(lat2Rad)
        );

        return {
            lat: lat2Rad * 180 / Math.PI,
            lon: lon2Rad * 180 / Math.PI
        };
    }

    // Mapbox API орқали баландликни олиш (янги Tilesets API)
    async getElevation(lat, lon) {
        // Mapbox Terrain-RGB tileset дан фойдаланиш
        const zoom = 14;
        const tileX = Math.floor((lon + 180) / 360 * Math.pow(2, zoom));
        const tileY = Math.floor((1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * Math.pow(2, zoom));

        try {
            // Birinchi usul: Tilequery API
            const url = `https://api.mapbox.com/v4/mapbox.mapbox-terrain-v2/tilequery/${lon},${lat}.json?layers=contour&limit=50&access_token=${this.apiKey}`;

            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data.features && data.features.length > 0) {
                // Энг яқин контур чизиғини топиш
                let closestElevation = 0;
                let minDistance = Infinity;

                data.features.forEach(feature => {
                    if (feature.properties && feature.properties.ele !== undefined) {
                        const featureCoords = feature.geometry.coordinates;
                        let featureLat, featureLon;

                        if (feature.geometry.type === 'Point') {
                            featureLon = featureCoords[0];
                            featureLat = featureCoords[1];
                        } else if (feature.geometry.type === 'LineString') {
                            // Чизиқнинг ўртасини олиш
                            const midIndex = Math.floor(featureCoords.length / 2);
                            featureLon = featureCoords[midIndex][0];
                            featureLat = featureCoords[midIndex][1];
                        } else {
                            return; // Бошқа геометрия турларини ўтказиб юбориш
                        }

                        const distance = this.calculateDistance(lat, lon, featureLat, featureLon);

                        if (distance < minDistance) {
                            minDistance = distance;
                            closestElevation = feature.properties.ele;
                        }
                    }
                });

                return closestElevation;
            }

            return 0; // Агар маълумот топилмаса
        } catch (error) {
            console.error('Баландлик олишда хато:', error);
            throw error;
        }
    }

    // Mapbox Elevation API дан фойдаланиш (янги усул)
    async getElevationNew(lat, lon) {
        const url = `https://api.mapbox.com/v4/mapbox.terrain-rgb/${Math.floor((lon + 180) / 360 * Math.pow(2, 14))}/${Math.floor((1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * Math.pow(2, 14))}/14.pngraw?access_token=${this.apiKey}`;

        try {
            // Бу ерда RGB маълумотларини олиш ва баландликка айлантириш керак
            // Ҳозирча оддий усулни ишлатамиз
            return await this.getSimpleElevation(lat, lon);
        } catch (error) {
            console.error('Баландлик олишда хато:', error);
            return 0;
        }
    }

    // Оддий баландлик олиш усули
    async getSimpleElevation(lat, lon) {
        // Mapbox Tilequery API дан фойдаланиш
        const url = `https://api.mapbox.com/v4/mapbox.mapbox-terrain-v2/tilequery/${lon},${lat}.json?layers=contour&limit=50&access_token=${this.apiKey}`;

        try {
            const response = await fetch(url);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            const data = await response.json();

            if (data.features && data.features.length > 0) {
                // Энг яқин контур чизиғини топиш
                let closestElevation = 0;
                let minDistance = Infinity;

                data.features.forEach(feature => {
                    if (feature.properties && feature.properties.ele !== undefined) {
                        const distance = this.calculateDistance(
                            lat, lon,
                            feature.geometry.coordinates[1],
                            feature.geometry.coordinates[0]
                        );

                        if (distance < minDistance) {
                            minDistance = distance;
                            closestElevation = feature.properties.ele;
                        }
                    }
                });

                return closestElevation;
            }

            return 0;
        } catch (error) {
            console.error('Баландлик олишда хато:', error);
            return 0;
        }
    }

    // Иккита нуқта орасидаги масофани хисоблаш
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371000;
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
            Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }

    async processData() {
        const progressSection = document.getElementById('progressSection');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        const processBtn = document.getElementById('processBtn');

        progressSection.style.display = 'block';
        processBtn.disabled = true;

        this.results = [];
        const errors = [];

        for (let i = 0; i < this.data.length; i++) {
            const item = this.data[i];

            try {
                // Иккинчи координатани хисоблаш
                const coord2 = this.calculateSecondCoordinate(
                    item.lat1, item.lon1, item.azimuth, item.distance
                );

                // Баландликларни олиш
                const elevation1 = await this.getElevation(item.lat1, item.lon1);
                await this.delay(200); // API лимитини олдини олиш учун

                const elevation2 = await this.getElevation(coord2.lat, coord2.lon);
                await this.delay(200);

                const reliefDifference = Math.abs(elevation2 - elevation1);

                this.results.push({
                    ...item,
                    lat2: coord2.lat,
                    lon2: coord2.lon,
                    elevation1: elevation1,
                    elevation2: elevation2,
                    reliefDifference: reliefDifference,
                    status: 'success'
                });

            } catch (error) {
                console.error(`Қатор ${item.rowIndex} да хато:`, error);
                errors.push(`Қатор ${item.rowIndex}: ${error.message}`);

                this.results.push({
                    ...item,
                    reliefDifference: 0,
                    status: 'error',
                    error: error.message
                });
            }

            // Прогрессни янгилаш
            const progress = ((i + 1) / this.data.length) * 100;
            progressFill.style.width = progress + '%';
            progressText.textContent = Math.round(progress) + '%';
        }

        this.showResults();
        if (errors.length > 0) {
            this.showErrors(errors);
        }

        processBtn.disabled = false;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    showResults() {
        const resultsSection = document.getElementById('resultsSection');
        const stats = document.getElementById('stats');

        const successCount = this.results.filter(r => r.status === 'success').length;
        const errorCount = this.results.filter(r => r.status === 'error').length;
        const avgRelief = this.results
            .filter(r => r.status === 'success')
            .reduce((sum, r) => sum + r.reliefDifference, 0) / successCount || 0;

        stats.innerHTML = `
            <div class="stat-item">
                <div class="stat-value">${this.results.length}</div>
                <div class="stat-label">Жами қаторлар</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${successCount}</div>
                <div class="stat-label">Муваффақиятли</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${errorCount}</div>
                <div class="stat-label">Хатолар</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">${avgRelief.toFixed(1)}м</div>
                <div class="stat-label">Ўртача рельеф фарки</div>
            </div>
        `;

        resultsSection.style.display = 'block';
        this.updatePreviewWithResults();
        this.showMap();
    }

    showMap() {
        if (!this.apiKey || this.results.length === 0) return;

        try {
            mapboxgl.accessToken = this.apiKey;

            // Барча координаталарни олиш
            const coordinates = [];
            this.results.forEach(result => {
                if (result.status === 'success') {
                    coordinates.push([result.lon1, result.lat1]); // Биринчи нуқта
                    coordinates.push([result.lon2, result.lat2]); // Иккинчи нуқта
                }
            });

            if (coordinates.length === 0) return;

            // Картанинг марказини ва масштабини аниқлаш
            const bounds = new mapboxgl.LngLatBounds();
            coordinates.forEach(coord => bounds.extend(coord));

            const map = new mapboxgl.Map({
                container: 'map',
                style: 'mapbox://styles/mapbox/terrain-v11',
                bounds: bounds,
                fitBoundsOptions: { padding: 50 }
            });

            map.on('load', () => {
                // Нуқталарни қўшиш
                this.results.forEach((result, index) => {
                    if (result.status === 'success') {
                        // Биринчи нуқта (кўк)
                        new mapboxgl.Marker({ color: '#007cbf' })
                            .setLngLat([result.lon1, result.lat1])
                            .setPopup(new mapboxgl.Popup().setHTML(`
                                <strong>Нуқта ${index + 1}А</strong><br>
                                Координата: ${result.lat1.toFixed(6)}, ${result.lon1.toFixed(6)}<br>
                                Баландлик: ${result.elevation1?.toFixed(1) || 'Н/М'} м
                            `))
                            .addTo(map);

                        // Иккинчи нуқта (қизил)
                        new mapboxgl.Marker({ color: '#e74c3c' })
                            .setLngLat([result.lon2, result.lat2])
                            .setPopup(new mapboxgl.Popup().setHTML(`
                                <strong>Нуқта ${index + 1}Б</strong><br>
                                Координата: ${result.lat2.toFixed(6)}, ${result.lon2.toFixed(6)}<br>
                                Баландлик: ${result.elevation2?.toFixed(1) || 'Н/М'} м<br>
                                Рельеф фарки: ${result.reliefDifference.toFixed(1)} м
                            `))
                            .addTo(map);

                        // Чизиқ қўшиш
                        map.addSource(`line-${index}`, {
                            'type': 'geojson',
                            'data': {
                                'type': 'Feature',
                                'properties': {},
                                'geometry': {
                                    'type': 'LineString',
                                    'coordinates': [
                                        [result.lon1, result.lat1],
                                        [result.lon2, result.lat2]
                                    ]
                                }
                            }
                        });

                        map.addLayer({
                            'id': `line-${index}`,
                            'type': 'line',
                            'source': `line-${index}`,
                            'layout': {
                                'line-join': 'round',
                                'line-cap': 'round'
                            },
                            'paint': {
                                'line-color': '#888',
                                'line-width': 2
                            }
                        });
                    }
                });
            });

        } catch (error) {
            console.error('Картани кўрсатишда хато:', error);
        }
    }

    updatePreviewWithResults() {
        const previewBody = document.getElementById('previewBody');
        previewBody.innerHTML = '';

        const previewData = this.results.slice(0, 10);

        previewData.forEach(item => {
            const row = document.createElement('tr');
            const coord2Text = item.lat2 ?
                `${item.lat2.toFixed(6)}, ${item.lon2.toFixed(6)}` : '-';
            const reliefText = item.status === 'success' ?
                `${item.reliefDifference.toFixed(1)}м` : '-';
            const statusText = item.status === 'success' ?
                'Тайёр' : `Хато: ${item.error || 'Номаълум'}`;
            const statusClass = item.status === 'success' ? 'status-success' : 'status-error';

            row.innerHTML = `
                <td>${item.originalData}</td>
                <td>${reliefText}</td>
                <td>${coord2Text}</td>
                <td class="${statusClass}">${statusText}</td>
            `;
            previewBody.appendChild(row);
        });

        if (this.results.length > 10) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="4" style="text-align: center; font-style: italic;">
                    ... ва яна ${this.results.length - 10} та қатор
                </td>
            `;
            previewBody.appendChild(row);
        }
    }

    showErrors(errors) {
        const errorSection = document.getElementById('errorSection');
        const errorList = document.getElementById('errorList');

        errorList.innerHTML = errors.map(error =>
            `<div class="error-item">${error}</div>`
        ).join('');

        errorSection.style.display = 'block';
    }

    showError(message) {
        this.showErrors([message]);
    }

    downloadResults() {
        const workbook = XLSX.utils.book_new();

        // Натижаларни Excel форматига тайёрлаш
        const excelData = [
            ['Биринчи координата', 'Рельеф фарки (м)', 'Иккинчи координата', 'Биринчи баландлик (м)', 'Иккинчи баландлик (м)', 'Статус']
        ];

        this.results.forEach(item => {
            const coord2Text = item.lat2 ?
                `${item.lat2.toFixed(6)};${item.lon2.toFixed(6)}` : '';
            const reliefText = item.status === 'success' ?
                item.reliefDifference.toFixed(1) : '';
            const elevation1Text = item.elevation1 !== undefined ?
                item.elevation1.toFixed(1) : '';
            const elevation2Text = item.elevation2 !== undefined ?
                item.elevation2.toFixed(1) : '';
            const statusText = item.status === 'success' ? 'Тайёр' : 'Хато';

            excelData.push([
                item.originalData,
                reliefText,
                coord2Text,
                elevation1Text,
                elevation2Text,
                statusText
            ]);
        });

        const worksheet = XLSX.utils.aoa_to_sheet(excelData);
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Натижалар');

        // Файлни юклаб олиш
        const fileName = `relief_results_${new Date().toISOString().slice(0, 10)}.xlsx`;
        XLSX.writeFile(workbook, fileName);
    }
}

// Дастурни ишга тушириш
document.addEventListener('DOMContentLoaded', () => {
    new ReliefCalculator();
});
