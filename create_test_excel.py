import pandas as pd

# Тест маълумотлари
test_data = [
    "41.2988891601562;69.1905517578125;180;890",
    "41.3000000000000;69.2000000000000;90;1000", 
    "41.2500000000000;69.1500000000000;45;750",
    "41.3500000000000;69.2500000000000;270;1200",
    "41.2800000000000;69.1800000000000;135;650"
]

# DataFrame яратиш
df = pd.DataFrame(test_data, columns=['Координата;Азимут;Масофа'])

# Excel файлга сақлаш
df.to_excel('test_data.xlsx', index=False)
print("test_data.xlsx файли яратилди!")
print("Маълумотлар:")
for i, data in enumerate(test_data, 1):
    print(f"{i}. {data}")
