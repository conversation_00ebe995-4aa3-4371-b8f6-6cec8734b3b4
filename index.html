<!DOCTYPE html>
<html lang="uz">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Рельеф Фарки Хисоблагич</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js'></script>
    <link href='https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css' rel='stylesheet' />
</head>

<body>
    <div class="container">
        <h1>Рельеф Фарки Хисоблагич</h1>
        <div class="upload-section">
            <label for="fileInput" class="file-label">
                Excel файлни танланг
                <input type="file" id="fileInput" accept=".xlsx,.xls" />
            </label>
            <div class="file-info" id="fileInfo"></div>
        </div>

        <div class="api-section">
            <label for="apiKey">Mapbox API калити:</label>
            <input type="text" id="apiKey" placeholder="pk.eyJ1..."
                value="pk.eyJ1Ijoic2FtY2hpazAiLCJhIjoiY21iOTN1NHNhMDlxbzJrcG96a21iOG91OSJ9.qY" />
        </div>

        <button id="processBtn" disabled>Хисоблашни бошлаш</button>

        <div class="progress-section" id="progressSection" style="display: none;">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">0%</div>
        </div>

        <div class="results-section" id="resultsSection" style="display: none;">
            <h3>Натижалар:</h3>
            <div class="stats" id="stats"></div>
            <div class="map-container">
                <div id="map" style="height: 400px; border-radius: 8px; margin: 20px 0;"></div>
            </div>
            <button id="downloadBtn">Натижани юклаб олиш</button>
        </div>

        <div class="preview-section" id="previewSection" style="display: none;">
            <h3>Маълумотлар кўриниши:</h3>
            <table id="previewTable">
                <thead>
                    <tr>
                        <th>Биринчи координата</th>
                        <th>Рельеф фарки (м)</th>
                        <th>Иккинчи координата</th>
                        <th>Статус</th>
                    </tr>
                </thead>
                <tbody id="previewBody"></tbody>
            </table>
        </div>

        <div class="error-section" id="errorSection" style="display: none;">
            <h3>Хатолар:</h3>
            <div id="errorList"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>

</html>